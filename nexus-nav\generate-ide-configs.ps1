# NEXUS Agent IDE Configuration Generator
# This script generates IDE configurations for all NEXUS agents

$agents = @('analyzer', 'architect', 'implementer', 'validator', 'optimizer', 'documenter')
$ides = @(
    @{ Name = 'github-copilot'; Path = '.github'; ChatExt = 'chatmode.md'; InstExt = 'instructions.md' },
    @{ Name = 'cursor'; Path = '.cursor/rules'; ChatExt = 'mdc'; InstExt = 'mdc' },
    @{ Name = 'claude-code'; Path = '.claude'; ChatExt = 'md'; InstExt = 'md' },
    @{ Name = 'augment'; Path = '.augment'; ChatExt = 'md'; InstExt = 'md' },
    @{ Name = 'trae'; Path = '.trae'; ChatExt = 'md'; InstExt = 'md' }
)

$baseDir = "C:\Users\<USER>\Downloads\ai-workflow-nav\ai-six-expert-system_02\nexus-nav"
$agentsDir = "$baseDir\.nexus-core\agents"

foreach ($ide in $ides) {
    $ideDir = "$baseDir\ide-configs\$($ide.Name)"
    
    # Create directories
    if ($ide.Name -eq 'github-copilot') {
        New-Item -ItemType Directory -Path "$ideDir\$($ide.Path)\chatmodes" -Force | Out-Null
        New-Item -ItemType Directory -Path "$ideDir\$($ide.Path)\instructions" -Force | Out-Null
    } else {
        New-Item -ItemType Directory -Path "$ideDir\$($ide.Path)" -Force | Out-Null
    }
    
    foreach ($agent in $agents) {
        $agentFile = "$agentsDir\$agent.md"
        $content = Get-Content $agentFile -Raw
        
        # Extract agent info
        // ...
        # if ($yamlMatch) {
        #     $yamlBlock = $matches[1]
        # }
        
        # For GitHub Copilot, create chatmode and instructions
        if ($ide.Name -eq 'github-copilot') {
            # Create chatmode file
            $chatmodeContent = @"
---
description: "Activates the $agent agent persona."
tools: ['changes', 'codebase', 'fetch', 'findTestFiles', 'githubRepo', 'problems', 'usages']
---

$content
"@
            $chatmodeFile = "$ideDir\$($ide.Path)\chatmodes\$agent.$($ide.ChatExt)"
            $chatmodeContent | Out-File -FilePath $chatmodeFile -Encoding utf8
            
            # Create instructions file
            $instructionsContent = @"
# $($agent.ToUpper()) Agent Rule

This rule is triggered when the user types `@$agent` and activates the $agent agent persona.

## Agent Activation

$content

## Usage

When the user types `@$agent`, activate this $agent persona and follow all instructions defined in the YAML configuration above.
"@
            $instructionsFile = "$ideDir\$($ide.Path)\instructions\$agent.$($ide.InstExt)"
            $instructionsContent | Out-File -FilePath $instructionsFile -Encoding utf8
        }
        else {
            # For other IDEs, just create the agent file
            $agentFileContent = @"
---
description: ""
globs: []
alwaysApply: false
---

# $($agent.ToUpper()) Agent Rule

This rule is triggered when the user types `@$agent` and activates the $agent agent persona.

## Agent Activation

$content

## Usage

When the user types `@$agent`, activate this $agent persona and follow all instructions defined in the YAML configuration above.
"@
            $agentFilePath = "$ideDir\$($ide.Path)\$agent.$($ide.InstExt)"
            $agentFileContent | Out-File -FilePath $agentFilePath -Encoding utf8
        }
    }
}

Write-Host "✅ Generated IDE configurations for all NEXUS agents across all IDEs"
