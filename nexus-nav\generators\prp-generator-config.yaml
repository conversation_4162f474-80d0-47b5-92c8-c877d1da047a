generator:
  name: "PRP Generator Agent"
  version: "1.0.0"
  type: "prompt-generator"
  framework: "NEXUS"

configuration:
  output_format: "markdown"
  template_structure: "feature-prompt"
  context_depth: "comprehensive"
  implementation_ready: true

tech_stack:
  frontend:
    framework: "Next.js 15.4+"
    ui_library: "React 19"
    language: "TypeScript 5.8+"
    routing: "App Router"
    components: "Server Components"
  
  backend:
    platform: "Supabase"
    database: "PostgreSQL"
    auth: "Supabase Auth"
    storage: "Supabase Storage"
    realtime: "Supabase Realtime"
  
  state_management:
    client: "Zustand 5+"
    server: "TanStack Query v5"
    validation: "Valibot v1.1.0"
  
  ui_styling:
    css: "Tailwind CSS 4.0+"
    components: "Shadcn/ui"
    primitives: "Radix UI"
  
  database:
    primary_keys: "XID (pg_idkit)"
    security: "Row Level Security (RLS)"
    search: "pgvector for AI features"

prp_sections:
  context_requirements:
    required: true
    includes:
      - feature_overview
      - business_objectives
      - user_stories
      - acceptance_criteria
      - technical_constraints
      - integration_requirements
  
  technical_implementation:
    required: true
    includes:
      - component_architecture
      - database_operations
      - api_specifications
      - state_management
      - error_handling
      - validation_patterns
  
  code_standards:
    required: true
    includes:
      - typescript_interfaces
      - react_patterns
      - nextjs_implementation
      - supabase_integration
      - testing_requirements
  
  security_performance:
    required: true
    includes:
      - authentication_requirements
      - authorization_patterns
      - input_validation
      - performance_optimization
      - accessibility_requirements
  
  implementation_checklist:
    required: true
    format: "checkbox_list"
    includes:
      - component_implementation
      - database_schema
      - api_endpoints
      - testing_coverage
      - security_compliance
      - performance_validation
      - documentation

prompt_characteristics:
  context_richness:
    project_awareness: true
    pattern_references: true
    integration_context: true
    existing_code_awareness: true
  
  implementation_guidance:
    step_by_step: true
    code_examples: true
    pattern_suggestions: true
    best_practices: true
  
  quality_requirements:
    type_safety: true
    error_handling: true
    testing_coverage: true
    security_validation: true
    performance_optimization: true

target_ides:
  cursor: true
  github_copilot: true
  claude_code: true
  augmentcode: true
  trae: true

output_requirements:
  file_format: "markdown"
  naming_convention: "[feature-name]-prp.md"
  structure: "hierarchical"
  code_examples: true
  implementation_ready: true
  context_complete: true

feature_types:
  authentication:
    includes:
      - user_registration
      - login_logout
      - password_reset
      - social_auth
      - session_management
  
  data_management:
    includes:
      - crud_operations
      - data_validation
      - real_time_updates
      - file_uploads
      - search_functionality
  
  user_interface:
    includes:
      - component_development
      - form_handling
      - responsive_design
      - accessibility
      - user_experience
  
  api_development:
    includes:
      - endpoint_creation
      - middleware_implementation
      - error_handling
      - validation
      - authentication

validation_criteria:
  completeness: true
  technical_accuracy: true
  implementation_clarity: true
  context_sufficiency: true
  quality_standards: true
