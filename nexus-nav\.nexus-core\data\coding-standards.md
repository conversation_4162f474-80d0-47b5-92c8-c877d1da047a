# NEXUS Coding Standards

## 💻 **Production-Ready Code Standards**

### Core Development Principles
```yaml
development_principles:
  quality_first:
    - Write clean, readable, maintainable code
    - Follow established patterns and conventions
    - Prioritize code quality over speed
    - Use TypeScript strict mode always
    - Implement proper error handling

  performance_optimization:
    - Optimize for millions of users
    - Use React Server Components by default
    - Implement proper caching strategies
    - Minimize client-side JavaScript
    - Optimize images and assets

  security_standards:
    - Validate all inputs on client and server
    - Use parameterized queries for database
    - Implement proper authentication and authorization
    - Follow OWASP security guidelines
    - Use HTTPS everywhere

  scalability_requirements:
    - Design for horizontal scaling
    - Use efficient database queries
    - Implement proper indexing
    - Consider caching at multiple levels
    - Plan for high availability

  code_organization:
    - Use consistent file and folder structure
    - Implement proper separation of concerns
    - Create reusable components and utilities
    - Document complex logic and decisions
    - Follow DRY principles but avoid over-abstraction
```

### TypeScript Excellence Standards
```yaml
typescript_standards:
  type_safety:
    strict_mode: required
    no_implicit_any: enforced
    no_unused_vars: error
    return_types: explicit
    function_parameters: typed
  
  interface_patterns:
    prefer_interfaces: over_types
    extensibility: required
    generic_constraints: when_needed
    discriminated_unions: for_state
  
  naming_conventions:
    variables: camelCase
    functions: camelCase
    interfaces: PascalCase
    types: PascalCase
    constants: SCREAMING_SNAKE_CASE
    files: kebab-case
```

### React Component Standards
```yaml
component_standards:
  server_components:
    default_choice: true
    async_functions: allowed
    data_fetching: preferred
    no_hooks: enforced
    export_pattern: "export default async function"
  
  client_components:
    use_client_directive: required
    hooks_allowed: true
    event_handlers: primary_use
    state_management: when_needed
    prop_types: typescript_interfaces
  
  component_structure:
    imports_order: "react, next, external, internal, relative"
    prop_interface: above_component
    default_export: preferred
    named_exports: for_utilities
```

### File Organization Standards
```yaml
file_organization:
  directory_structure:
    app_router: "app/ directory structure"
    components: "components/ with index exports"
    lib: "lib/ for utilities and configuration"
    types: "types/ for shared TypeScript definitions"
  
  naming_conventions:
    pages: "page.tsx in app router"
    layouts: "layout.tsx for route layouts"
    loading: "loading.tsx for loading states"
    error: "error.tsx for error boundaries"
    not_found: "not-found.tsx for 404 pages"
  
  import_patterns:
    absolute_imports: preferred
    index_exports: for_clean_imports
    barrel_exports: avoid_overuse
    dynamic_imports: for_code_splitting
```

### API Route Standards
```yaml
api_standards:
  route_structure:
    http_methods: explicit_exports
    error_handling: comprehensive
    input_validation: valibot_schemas
    response_format: consistent
  
  security_patterns:
    authentication: server_side_validation
    authorization: role_based_checks
    input_sanitization: all_inputs
    rate_limiting: implemented
  
  performance_patterns:
    database_queries: optimized
    response_caching: when_appropriate
    async_operations: non_blocking
    connection_pooling: configured
```

## 🎯 **Code Quality Patterns**

### Error Handling Standards
```typescript
// NEXUS Error Handling Pattern
export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message)
    this.name = 'AppError'
  }
}

// API Route Error Handler
export function handleAPIError(error: unknown): NextResponse {
  if (error instanceof AppError) {
    return NextResponse.json(
      { error: error.message, code: error.code },
      { status: error.statusCode }
    )
  }
  
  if (error instanceof ValidationError) {
    return NextResponse.json(
      { error: 'Invalid input', details: error.issues },
      { status: 400 }
    )
  }
  
  // Log unexpected errors
  console.error('Unexpected API error:', error)
  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  )
}
```

### Component Pattern Standards
```typescript
// NEXUS Server Component Pattern
interface UserProfileProps {
  userId: string
  showPrivateInfo?: boolean
}

export default async function UserProfile({ 
  userId, 
  showPrivateInfo = false 
}: UserProfileProps) {
  // Data fetching in Server Component
  const user = await getUserById(userId)
  const posts = await getUserPosts(userId, { limit: 10 })
  
  if (!user) {
    notFound()
  }
  
  return (
    <div className="user-profile">
      <UserHeader user={user} />
      <Suspense fallback={<PostsSkeleton />}>
        <UserPosts posts={posts} />
      </Suspense>
      {showPrivateInfo && (
        <PrivateUserInfo userId={userId} />
      )}
    </div>
  )
}

// NEXUS Client Component Pattern
'use client'

interface InteractiveButtonProps {
  onSubmit: (data: FormData) => Promise<void>
  disabled?: boolean
  children: React.ReactNode
}

export function InteractiveButton({ 
  onSubmit, 
  disabled = false, 
  children 
}: InteractiveButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  
  const handleSubmit = useCallback(async (data: FormData) => {
    setIsLoading(true)
    try {
      await onSubmit(data)
    } catch (error) {
      console.error('Submit error:', error)
    } finally {
      setIsLoading(false)
    }
  }, [onSubmit])
  
  return (
    <button 
      onClick={() => handleSubmit(new FormData())}
      disabled={disabled || isLoading}
      className="interactive-button"
    >
      {isLoading ? 'Loading...' : children}
    </button>
  )
}
```

### Database Interaction Standards
```typescript
// NEXUS Database Pattern
import { createClient } from '@/lib/supabase/server'
import { DatabaseUser, CreateUserInput } from '@/types/database'

export async function createUser(input: CreateUserInput): Promise<DatabaseUser> {
  const supabase = createClient()
  
  // Validate input
  const validatedInput = CreateUserSchema.parse(input)
  
  // Check authentication
  const { data: { user: authUser }, error: authError } = 
    await supabase.auth.getUser()
  
  if (authError || !authUser) {
    throw new AppError('Authentication required', 401)
  }
  
  // Database operation with RLS
  const { data, error } = await supabase
    .from('users')
    .insert({
      ...validatedInput,
      created_by: authUser.id
    })
    .select()
    .single()
  
  if (error) {
    console.error('Database error:', error)
    throw new AppError('Failed to create user', 500)
  }
  
  return data
}
```

## 📋 **Code Review Standards**

### Required Checks
```yaml
code_review_checklist:
  type_safety:
    - No 'any' types used
    - All functions have explicit return types
    - Props interfaces defined
    - Proper generic constraints
  
  security:
    - Input validation with Valibot
    - Authentication checks on protected routes
    - Authorization verification
    - No hardcoded secrets
  
  performance:
    - Server Components for data fetching
    - Client Components only when needed
    - Proper memoization techniques
    - Dynamic imports for code splitting
  
  maintainability:
    - Single responsibility principle
    - DRY principle applied
    - Proper error handling
    - Comprehensive documentation
```

### Testing Standards
```yaml
testing_requirements:
  unit_tests:
    coverage_minimum: 80%
    test_framework: vitest
    mocking_strategy: minimal
    assertion_library: expect
  
  integration_tests:
    api_routes: all_endpoints
    database_operations: critical_paths
    authentication_flows: complete
    error_scenarios: covered
  
  e2e_tests:
    critical_user_flows: tested
    cross_browser: chrome_firefox_safari
    mobile_responsive: verified
    accessibility: wcag_compliant
```

## 🛠️ **Development Workflow Standards**

### Git Workflow
```yaml
git_standards:
  branch_naming:
    feature: "feature/description"
    bugfix: "bugfix/description"
    hotfix: "hotfix/description"
    chore: "chore/description"
  
  commit_messages:
    format: "type(scope): description"
    types: [feat, fix, docs, style, refactor, test, chore]
    imperative_mood: required
    max_length: 72_characters
  
  pull_requests:
    title: descriptive_and_clear
    description: problem_and_solution
    reviewers: minimum_two
    tests: passing_required
```

### Code Documentation
```yaml
documentation_standards:
  inline_comments:
    when_needed: complex_logic_only
    avoid: obvious_statements
    format: "// Clear, concise explanation"
    todo_format: "// TODO: Specific action needed"
  
  function_documentation:
    jsdoc_format: preferred
    parameters: described
    return_values: documented
    examples: for_complex_functions
  
  readme_requirements:
    installation_steps: detailed
    usage_examples: provided
    api_documentation: complete
    contributing_guide: included
```

## ⚙️ **Tool Configuration Standards**

### ESLint Configuration
```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-return-type": "warn",
    "@typescript-eslint/no-explicit-any": "error",
    "prefer-const": "error",
    "no-console": "warn"
  }
}
```

### Prettier Configuration
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

This coding standards file provides the Implementer agent with comprehensive guidelines for producing high-quality, maintainable, and consistent code.
