# NEXUS Component Patterns

## ⚛️ **React Server Component Patterns**

### Data Fetching Patterns
```typescript
// NEXUS Pattern: Server Component with Parallel Data Fetching
export default async function ProductPage({ 
  params 
}: { params: { id: string } }) {
  // Parallel data fetching for better performance
  const [product, reviews, relatedProducts] = await Promise.all([
    getProduct(params.id),
    getProductReviews(params.id),
    getRelatedProducts(params.id)
  ])
  
  if (!product) {
    notFound()
  }
  
  return (
    <div className="product-page">
      <ProductHeader product={product} />
      <Suspense fallback={<ReviewsSkeleton />}>
        <ProductReviews reviews={reviews} />
      </Suspense>
      <Suspense fallback={<RelatedProductsSkeleton />}>
        <RelatedProducts products={relatedProducts} />
      </Suspense>
    </div>
  )
}
```

### Composition Patterns
```typescript
// NEXUS Pattern: Server as Container, Client as Leaf
export default async function DashboardPage() {
  const user = await getCurrentUser()
  const stats = await getUserStats(user.id)
  
  return (
    <div className="dashboard">
      {/* Server Component with data */}
      <DashboardHeader user={user} stats={stats} />
      
      {/* Client Component for interactivity */}
      <InteractiveDashboard initialStats={stats} />
      
      {/* Streaming sections */}
      <Suspense fallback={<ChartsSkeleton />}>
        <DashboardCharts userId={user.id} />
      </Suspense>
    </div>
  )
}

// Client Component for interactive features
'use client'
export function InteractiveDashboard({ 
  initialStats 
}: { initialStats: UserStats }) {
  const [stats, setStats] = useState(initialStats)
  const [filters, setFilters] = useState<Filters>({})
  
  return (
    <div className="interactive-dashboard">
      <FilterControls 
        filters={filters} 
        onFiltersChange={setFilters} 
      />
      <StatsDisplay stats={stats} />
    </div>
  )
}
```

### Error Boundary Patterns
```typescript
// NEXUS Pattern: Component-Level Error Boundaries
export default function ProductsLayout({
  children
}: {
  children: React.ReactNode
}) {
  return (
    <div className="products-layout">
      <ProductsHeader />
      <ErrorBoundary
        fallback={<ProductsErrorFallback />}
        onError={(error) => logError('Products layout error', error)}
      >
        <Suspense fallback={<ProductsLoadingSkeleton />}>
          {children}
        </Suspense>
      </ErrorBoundary>
    </div>
  )
}

// Custom Error Boundary Component
export function ProductsErrorFallback() {
  return (
    <div className="error-fallback">
      <h2>Something went wrong loading products</h2>
      <button 
        onClick={() => window.location.reload()}
        className="retry-button"
      >
        Try Again
      </button>
    </div>
  )
}
```

## 🏗️ **Component Architecture Patterns**

### Atomic Design Pattern
```typescript
// NEXUS Pattern: Atomic Component Structure

// Atoms - Basic building blocks
export function Button({ 
  variant = 'primary', 
  size = 'medium', 
  children, 
  ...props 
}: ButtonProps) {
  return (
    <button 
      className={`btn btn-${variant} btn-${size}`}
      {...props}
    >
      {children}
    </button>
  )
}

// Molecules - Simple combinations
export function SearchInput({ 
  onSearch, 
  placeholder = 'Search...' 
}: SearchInputProps) {
  const [query, setQuery] = useState('')
  
  return (
    <div className="search-input">
      <Input
        value={query}
        onChange={setQuery}
        placeholder={placeholder}
      />
      <Button onClick={() => onSearch(query)}>
        Search
      </Button>
    </div>
  )
}

// Organisms - Complex components
export function ProductCard({ product }: ProductCardProps) {
  return (
    <Card className="product-card">
      <ProductImage src={product.image} alt={product.name} />
      <ProductInfo name={product.name} price={product.price} />
      <ProductActions productId={product.id} />
    </Card>
  )
}
```

### Compound Component Pattern
```typescript
// NEXUS Pattern: Compound Components for Flexibility
interface TabsContextType {
  activeTab: string
  setActiveTab: (tab: string) => void
}

const TabsContext = createContext<TabsContextType | null>(null)

export function Tabs({ 
  defaultTab, 
  children 
}: TabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab)
  
  return (
    <TabsContext.Provider value={{ activeTab, setActiveTab }}>
      <div className="tabs">
        {children}
      </div>
    </TabsContext.Provider>
  )
}

export function TabsList({ children }: TabsListProps) {
  return <div className="tabs-list">{children}</div>
}

export function TabsTrigger({ 
  value, 
  children 
}: TabsTriggerProps) {
  const context = useContext(TabsContext)
  if (!context) throw new Error('TabsTrigger must be used within Tabs')
  
  const { activeTab, setActiveTab } = context
  
  return (
    <button
      className={`tab-trigger ${activeTab === value ? 'active' : ''}`}
      onClick={() => setActiveTab(value)}
    >
      {children}
    </button>
  )
}

export function TabsContent({ 
  value, 
  children 
}: TabsContentProps) {
  const context = useContext(TabsContext)
  if (!context) throw new Error('TabsContent must be used within Tabs')
  
  const { activeTab } = context
  
  if (activeTab !== value) return null
  
  return <div className="tab-content">{children}</div>
}

// Usage:
// <Tabs defaultTab="overview">
//   <TabsList>
//     <TabsTrigger value="overview">Overview</TabsTrigger>
//     <TabsTrigger value="details">Details</TabsTrigger>
//   </TabsList>
//   <TabsContent value="overview">Overview content</TabsContent>
//   <TabsContent value="details">Details content</TabsContent>
// </Tabs>
```

### Render Props Pattern
```typescript
// NEXUS Pattern: Render Props for Reusable Logic
interface DataFetcherProps<T> {
  url: string
  children: (state: {
    data: T | null
    loading: boolean
    error: Error | null
    refetch: () => void
  }) => React.ReactNode
}

export function DataFetcher<T>({ url, children }: DataFetcherProps<T>) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)
  
  const fetchData = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch(url)
      if (!response.ok) throw new Error('Failed to fetch')
      const result = await response.json()
      setData(result)
    } catch (err) {
      setError(err as Error)
    } finally {
      setLoading(false)
    }
  }, [url])
  
  useEffect(() => {
    fetchData()
  }, [fetchData])
  
  return children({ data, loading, error, refetch: fetchData })
}

// Usage:
// <DataFetcher<User[]> url="/api/users">
//   {({ data, loading, error, refetch }) => {
//     if (loading) return <LoadingSpinner />
//     if (error) return <ErrorMessage error={error} onRetry={refetch} />
//     return <UserList users={data || []} />
//   }}
// </DataFetcher>
```

## 🎨 **UI Component Patterns**

### Form Patterns
```typescript
// NEXUS Pattern: Type-Safe Form with Validation
import { object, string, email, parse } from 'valibot'

const CreateUserSchema = object({
  name: string([minLength(1, 'Name is required')]),
  email: string([email('Invalid email address')]),
  role: string([includes(['admin', 'user'], 'Invalid role')])
})

type CreateUserInput = InferInput<typeof CreateUserSchema>

export function CreateUserForm({ 
  onSubmit 
}: { onSubmit: (data: CreateUserInput) => Promise<void> }) {
  const [formData, setFormData] = useState<Partial<CreateUserInput>>({})
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setErrors({})
    
    try {
      const validatedData = parse(CreateUserSchema, formData)
      await onSubmit(validatedData)
    } catch (error) {
      if (error instanceof ValiError) {
        const fieldErrors: Record<string, string> = {}
        error.issues.forEach(issue => {
          if (issue.path) {
            fieldErrors[issue.path[0].key] = issue.message
          }
        })
        setErrors(fieldErrors)
      }
    } finally {
      setIsSubmitting(false)
    }
  }
  
  return (
    <form onSubmit={handleSubmit} className="create-user-form">
      <FormField
        label="Name"
        value={formData.name || ''}
        onChange={(value) => setFormData(prev => ({ ...prev, name: value }))}
        error={errors.name}
        required
      />
      
      <FormField
        label="Email"
        type="email"
        value={formData.email || ''}
        onChange={(value) => setFormData(prev => ({ ...prev, email: value }))}
        error={errors.email}
        required
      />
      
      <SelectField
        label="Role"
        value={formData.role || ''}
        onChange={(value) => setFormData(prev => ({ ...prev, role: value }))}
        error={errors.role}
        options={[
          { value: 'user', label: 'User' },
          { value: 'admin', label: 'Admin' }
        ]}
        required
      />
      
      <Button 
        type="submit" 
        disabled={isSubmitting}
        className="submit-button"
      >
        {isSubmitting ? 'Creating...' : 'Create User'}
      </Button>
    </form>
  )
}
```

### Modal/Dialog Patterns
```typescript
// NEXUS Pattern: Accessible Modal Component
interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
}

export function Modal({ isOpen, onClose, title, children }: ModalProps) {
  const modalRef = useRef<HTMLDivElement>(null)
  
  // Close on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose()
      }
    }
    
    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, onClose])
  
  // Focus management
  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus()
    }
  }, [isOpen])
  
  // Prevent body scroll
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
      return () => {
        document.body.style.overflow = 'unset'
      }
    }
  }, [isOpen])
  
  if (!isOpen) return null
  
  return (
    <div className="modal-overlay" onClick={onClose}>
      <div 
        ref={modalRef}
        className="modal-content"
        onClick={(e) => e.stopPropagation()}
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
        tabIndex={-1}
      >
        <div className="modal-header">
          <h2 id="modal-title">{title}</h2>
          <button 
            onClick={onClose}
            className="modal-close"
            aria-label="Close modal"
          >
            ✕
          </button>
        </div>
        <div className="modal-body">
          {children}
        </div>
      </div>
    </div>
  )
}
```

## 🔄 **State Management Patterns**

### Local State Patterns
```typescript
// NEXUS Pattern: Optimistic Updates
export function TodoItem({ 
  todo, 
  onUpdate, 
  onDelete 
}: TodoItemProps) {
  const [optimisticTodo, setOptimisticTodo] = useState(todo)
  const [isUpdating, setIsUpdating] = useState(false)
  
  const handleToggle = async () => {
    // Optimistic update
    setOptimisticTodo(prev => ({ ...prev, completed: !prev.completed }))
    setIsUpdating(true)
    
    try {
      await onUpdate(todo.id, { completed: !todo.completed })
    } catch (error) {
      // Revert on error
      setOptimisticTodo(todo)
      console.error('Failed to update todo:', error)
    } finally {
      setIsUpdating(false)
    }
  }
  
  return (
    <div className={`todo-item ${optimisticTodo.completed ? 'completed' : ''}`}>
      <input
        type="checkbox"
        checked={optimisticTodo.completed}
        onChange={handleToggle}
        disabled={isUpdating}
      />
      <span>{optimisticTodo.text}</span>
    </div>
  )
}
```

### Global State Patterns
```typescript
// NEXUS Pattern: Zustand Store with TypeScript
interface UserStore {
  user: User | null
  isLoading: boolean
  error: string | null
  
  setUser: (user: User) => void
  clearUser: () => void
  updateUser: (updates: Partial<User>) => void
  fetchUser: (id: string) => Promise<void>
}

export const useUserStore = create<UserStore>((set, get) => ({
  user: null,
  isLoading: false,
  error: null,
  
  setUser: (user) => set({ user, error: null }),
  
  clearUser: () => set({ user: null, error: null }),
  
  updateUser: (updates) => set((state) => ({
    user: state.user ? { ...state.user, ...updates } : null
  })),
  
  fetchUser: async (id) => {
    set({ isLoading: true, error: null })
    
    try {
      const user = await getUserById(id)
      set({ user, isLoading: false })
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : 'Unknown error',
        isLoading: false 
      })
    }
  }
}))

// Selective subscription pattern
export function UserProfile() {
  const user = useUserStore(state => state.user)
  const updateUser = useUserStore(state => state.updateUser)
  
  // Component only re-renders when user changes
  return (
    <div>
      <h1>{user?.name}</h1>
      <button onClick={() => updateUser({ name: 'New Name' })}>
        Update Name
      </button>
    </div>
  )
}
```

This component patterns file provides the Implementer agent with comprehensive React patterns for building scalable, maintainable, and performant components.
