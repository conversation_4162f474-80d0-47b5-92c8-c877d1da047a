title: "Database Design Document"
description: "Template for designing PostgreSQL databases with Supabase"
version: "1.0.0"
framework: "NEXUS"

sections:
  - id: overview
    title: "Database Overview"
    instruction: "Provide high-level database description and purpose"
    template: |
      # {{database_name}} Database Design
      
      ## Purpose
      {{database_purpose}}
      
      ## Scale Requirements
      {{scale_requirements}}
      
      ## Key Entities
      {{key_entities}}

  - id: schema_design
    title: "Schema Design"
    instruction: "Define database schema with tables and relationships"
    template: |
      ## Database Schema
      
      ### Tables Overview
      {{tables_overview}}
      
      ### Entity Relationship Diagram
      ```mermaid
      erDiagram
      {{erd_diagram}}
      ```
      
      ### Primary Key Strategy
      - **Type**: XID (Extended ID) for global uniqueness
      - **Format**: 20-character alphanumeric string
      - **Benefits**: URL-safe, sortable, collision-resistant

  - id: table_definitions
    title: "Table Definitions"
    instruction: "Define each table with columns, types, and constraints"
    repeatable: true
    template: |
      ### {{table_name}} Table
      
      **Purpose**: {{table_purpose}}
      
      ```sql
      CREATE TABLE {{table_name}} (
        {{column_definitions}}
      );
      ```
      
      **Indexes**:
      ```sql
      {{table_indexes}}
      ```
      
      **Constraints**:
      {{table_constraints}}

  - id: relationships
    title: "Relationships"
    instruction: "Define foreign key relationships and constraints"
    template: |
      ## Table Relationships
      
      ### Foreign Key Constraints
      ```sql
      {{foreign_key_constraints}}
      ```
      
      ### Relationship Types
      {{relationship_types}}
      
      ### Referential Integrity
      {{referential_integrity}}

  - id: security
    title: "Row Level Security (RLS)"
    instruction: "Define RLS policies for multi-tenant security"
    template: |
      ## Row Level Security Policies
      
      ### Security Strategy
      {{security_strategy}}
      
      ### RLS Policies
      ```sql
      {{rls_policies}}
      ```
      
      ### User Roles and Permissions
      {{user_roles_permissions}}

  - id: performance
    title: "Performance Optimization"
    instruction: "Define indexing and optimization strategies for millions of users"
    template: |
      ## Performance Optimization
      
      ### Indexing Strategy
      ```sql
      {{indexing_strategy}}
      ```
      
      ### Query Optimization
      {{query_optimization}}
      
      ### Partitioning Strategy
      {{partitioning_strategy}}
      
      ### Connection Pooling
      {{connection_pooling}}

  - id: migrations
    title: "Database Migrations"
    instruction: "Define migration strategy and versioning"
    template: |
      ## Migration Strategy
      
      ### Migration Files
      {{migration_files}}
      
      ### Versioning Strategy
      {{versioning_strategy}}
      
      ### Rollback Procedures
      {{rollback_procedures}}
      
      ### Data Migration
      {{data_migration}}

  - id: backup_recovery
    title: "Backup and Recovery"
    instruction: "Define backup and disaster recovery procedures"
    template: |
      ## Backup and Recovery
      
      ### Backup Strategy
      {{backup_strategy}}
      
      ### Recovery Procedures
      {{recovery_procedures}}
      
      ### Data Retention
      {{data_retention}}
      
      ### Disaster Recovery
      {{disaster_recovery}}

  - id: monitoring
    title: "Monitoring and Maintenance"
    instruction: "Define database monitoring and maintenance procedures"
    template: |
      ## Monitoring and Maintenance
      
      ### Performance Monitoring
      {{performance_monitoring}}
      
      ### Health Checks
      {{health_checks}}
      
      ### Maintenance Procedures
      {{maintenance_procedures}}
      
      ### Alerting Strategy
      {{alerting_strategy}}

placeholders:
  database_name: "Database name (e.g., SaaS Application Database)"
  database_purpose: "Primary purpose and business requirements"
  scale_requirements: "Expected scale (users, transactions, data volume)"
  key_entities: "Main business entities and their relationships"
  tables_overview: "List of all tables and their purposes"
  erd_diagram: "Mermaid ERD syntax for entity relationships"
  table_name: "Name of the database table"
  table_purpose: "Purpose and business logic of the table"
  column_definitions: "SQL column definitions with types and constraints"
  table_indexes: "Index definitions for the table"
  table_constraints: "Check constraints and business rules"
  foreign_key_constraints: "All foreign key relationships"
  relationship_types: "One-to-one, one-to-many, many-to-many relationships"
  referential_integrity: "How referential integrity is maintained"
  security_strategy: "Overall security approach for multi-tenancy"
  rls_policies: "Row Level Security policy definitions"
  user_roles_permissions: "User roles and their database permissions"
  indexing_strategy: "Index creation for performance optimization"
  query_optimization: "Query optimization techniques and patterns"
  partitioning_strategy: "Table partitioning for large datasets"
  connection_pooling: "Connection pooling configuration"
  migration_files: "Migration file naming and organization"
  versioning_strategy: "Database version control strategy"
  rollback_procedures: "How to rollback failed migrations"
  data_migration: "Data migration procedures and validation"
  backup_strategy: "Automated backup procedures and schedules"
  recovery_procedures: "Step-by-step recovery procedures"
  data_retention: "Data retention policies and archival"
  disaster_recovery: "Disaster recovery planning and procedures"
  performance_monitoring: "Database performance monitoring setup"
  health_checks: "Database health check procedures"
  maintenance_procedures: "Regular maintenance tasks and schedules"
  alerting_strategy: "Database alerting and notification setup"

validation:
  required_sections: ["overview", "schema_design", "table_definitions", "security", "performance"]
  rls_required: true
  xid_primary_keys: true
  performance_review: true
  security_review: true
