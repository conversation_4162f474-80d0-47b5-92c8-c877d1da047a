{"name": "nexus-framework", "version": "1.0.0", "description": "NEXUS Framework - 10000x Better AI Development Framework for Cursor/Claude Code", "main": "tools/nexus-installer.js", "bin": {"nexus": "./bin/nexus.js", "nexus-framework": "./bin/nexus.js"}, "scripts": {"install:nexus": "node tools/nexus-installer.js", "generate:prd": "node tools/generators/prd-generator.js", "generate:architecture": "node tools/generators/architecture-generator.js", "generate:prp": "node tools/generators/prp-generator.js", "generate:tasks": "node tools/generators/task-generator.js"}, "keywords": ["nexus", "ai", "development", "framework", "cursor", "claude", "nextjs", "react", "typescript", "supabase", "micro-agents"], "author": "NEXUS Team", "license": "MIT", "dependencies": {"fs-extra": "^11.3.0", "js-yaml": "^4.1.0", "chalk": "^5.4.1", "ora": "^8.2.0", "inquirer": "12.7.0", "commander": "^14.0.0"}, "engines": {"node": ">=24.2.0"}, "repository": {"type": "git", "url": "https://github.com/nexus-team/nexus-framework.git"}, "files": ["apex-ai-system/", "tools/", "README.md"]}