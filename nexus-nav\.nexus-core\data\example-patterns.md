# NEXUS Documentation Example Patterns

## 📚 **Reusable Documentation Patterns**

### Code Example Patterns

#### Complete Component Example
```typescript
// ✅ Pattern: Complete, working component with all imports
// components/UserDashboard.tsx
'use client';

import { useState, useEffect } from 'react';
import { User } from '@/types/user';
import { useUserStore } from '@/stores/useUserStore';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface UserDashboardProps {
  initialUser: User;
}

export function UserDashboard({ initialUser }: UserDashboardProps) {
  const { user, updateUser } = useUserStore();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (initialUser) {
      updateUser(initialUser);
    }
  }, [initialUser, updateUser]);

  const handleProfileUpdate = async () => {
    setIsLoading(true);
    try {
      // Update user profile logic
      await updateUserProfile(user.id, { lastLogin: new Date() });
    } catch (error) {
      console.error('Failed to update profile:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Welcome, {user?.name}</CardTitle>
      </CardHeader>
      <CardContent>
        <Button 
          onClick={handleProfileUpdate} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? 'Updating...' : 'Update Profile'}
        </Button>
      </CardContent>
    </Card>
  );
}
```

#### API Route Example
```typescript
// ✅ Pattern: Complete API route with validation and error handling
// app/api/users/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { object, string } from 'valibot';

const UpdateUserSchema = object({
  name: string(),
  email: string(),
});

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerComponentClient({ cookies });
    
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', params.id)
      .single();

    if (error) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const validatedData = parse(UpdateUserSchema, body);
    
    const supabase = createServerComponentClient({ cookies });
    
    const { data: user, error } = await supabase
      .from('users')
      .update(validatedData)
      .eq('id', params.id)
      .select()
      .single();

    if (error) {
      return NextResponse.json(
        { error: 'Failed to update user' },
        { status: 400 }
      );
    }

    return NextResponse.json(user);
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request data' },
      { status: 400 }
    );
  }
}
```

### Tutorial Step Patterns

#### Setup Step Pattern
```markdown
## Step 1: Project Setup

In this step, you'll create a new Next.js project with the NEXUS tech stack.

### 1.1 Create the Project

```bash
npx create-next-app@latest my-saas-app \
  --typescript \
  --tailwind \
  --app \
  --src-dir \
  --import-alias "@/*"
```

### 1.2 Install Dependencies

```bash
cd my-saas-app
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs zustand @tanstack/react-query valibot
```

### 1.3 Verify Installation

Your project structure should look like this:

```
my-saas-app/
├── src/
│   ├── app/
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   └── components/
├── package.json
└── tailwind.config.ts
```

**✅ Checkpoint**: Run `npm run dev` and verify the app starts at `http://localhost:3000`
```

#### Implementation Step Pattern
```markdown
## Step 3: Implement User Authentication

Now you'll add user authentication using Supabase Auth.

### 3.1 Create Supabase Client

Create the Supabase client configuration:

```typescript
// src/lib/supabase/client.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';

export const supabase = createClientComponentClient();
```

### 3.2 Create Auth Hook

```typescript
// src/hooks/useAuth.ts
'use client';

import { useState, useEffect } from 'react';
import { User } from '@supabase/auth-helpers-nextjs';
import { supabase } from '@/lib/supabase/client';

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
      setLoading(false);
    };

    getUser();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  return { user, loading };
}
```

### 3.3 Test the Implementation

Add this to your page to test authentication:

```typescript
// src/app/page.tsx
'use client';

import { useAuth } from '@/hooks/useAuth';

export default function HomePage() {
  const { user, loading } = useAuth();

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      {user ? (
        <p>Welcome, {user.email}!</p>
      ) : (
        <p>Please sign in</p>
      )}
    </div>
  );
}
```

**✅ Checkpoint**: The page should display "Please sign in" when not authenticated.
```

### Error Handling Patterns

#### API Error Response Pattern
```typescript
// ✅ Pattern: Consistent error response structure
interface APIError {
  error: string;
  code: string;
  details?: any;
  timestamp: string;
}

// Usage in API routes
export async function POST(request: NextRequest) {
  try {
    // API logic here
  } catch (error) {
    if (error instanceof ValidationError) {
      return NextResponse.json({
        error: error.message,
        code: 'VALIDATION_ERROR',
        details: error.field,
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    return NextResponse.json({
      error: 'Internal server error',
      code: 'INTERNAL_ERROR',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
```

#### Client Error Handling Pattern
```typescript
// ✅ Pattern: Client-side error handling with user feedback
'use client';

import { useState } from 'react';
import { toast } from '@/components/ui/use-toast';

export function UserForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (formData: FormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Something went wrong');
      }

      const user = await response.json();
      toast({
        title: 'Success',
        description: 'User created successfully',
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      setError(message);
      toast({
        title: 'Error',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form action={handleSubmit}>
      {error && (
        <div className="text-red-600 mb-4">
          {error}
        </div>
      )}
      {/* Form fields */}
      <button 
        type="submit" 
        disabled={isSubmitting}
        className="w-full"
      >
        {isSubmitting ? 'Creating...' : 'Create User'}
      </button>
    </form>
  );
}
```

### Configuration Patterns

#### Environment Variables Pattern
```bash
# ✅ Pattern: Complete .env.example with descriptions
# .env.example

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Database
DATABASE_URL=your_database_connection_string

# Authentication
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# External APIs
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Email
RESEND_API_KEY=your_resend_api_key
FROM_EMAIL=<EMAIL>

# Analytics
NEXT_PUBLIC_GA_ID=your_google_analytics_id
```

#### TypeScript Configuration Pattern
```json
// ✅ Pattern: Complete tsconfig.json for NEXUS projects
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["dom", "dom.iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/hooks/*": ["./src/hooks/*"],
      "@/stores/*": ["./src/stores/*"],
      "@/types/*": ["./src/types/*"],
      "@/utils/*": ["./src/utils/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### Testing Patterns

#### Component Test Pattern
```typescript
// ✅ Pattern: Complete component test with all scenarios
// __tests__/components/UserProfile.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { UserProfile } from '@/components/UserProfile';
import { useUserStore } from '@/stores/useUserStore';

// Mock the store
jest.mock('@/stores/useUserStore');
const mockUseUserStore = useUserStore as jest.MockedFunction<typeof useUserStore>;

// Mock API calls
global.fetch = jest.fn();

describe('UserProfile', () => {
  const mockUser = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
  };

  beforeEach(() => {
    mockUseUserStore.mockReturnValue({
      user: mockUser,
      updateUser: jest.fn(),
    });
    
    (fetch as jest.Mock).mockClear();
  });

  it('renders user information', () => {
    render(<UserProfile initialUser={mockUser} />);
    
    expect(screen.getByText('Welcome, John Doe')).toBeInTheDocument();
  });

  it('handles profile update successfully', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockUser,
    });

    render(<UserProfile initialUser={mockUser} />);
    
    const updateButton = screen.getByText('Update Profile');
    fireEvent.click(updateButton);

    expect(screen.getByText('Updating...')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('Update Profile')).toBeInTheDocument();
    });

    expect(fetch).toHaveBeenCalledWith('/api/users/1', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ lastLogin: expect.any(String) }),
    });
  });

  it('handles profile update error', async () => {
    (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

    render(<UserProfile initialUser={mockUser} />);
    
    const updateButton = screen.getByText('Update Profile');
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(screen.getByText('Update Profile')).toBeInTheDocument();
    });

    // Verify error handling (could check for error message display)
  });
});
```

These patterns provide consistent, reusable examples for all types of NEXUS documentation, ensuring quality and consistency across all materials.
