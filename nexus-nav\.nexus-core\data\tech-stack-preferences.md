# NEXUS Tech Stack Preferences

## 🏗️ **Recommended Technology Stack**

### Frontend Framework
```yaml
primary_stack:
  framework: "Next.js 15+"
  rendering: "App Router with React Server Components"
  styling: "Tailwind CSS 3.4+"
  state_management: "Zustand + TanStack Query"
  validation: "Valibot"
  
  reasoning:
    nextjs: "Best-in-class React framework with SSR, SSG, and Edge Runtime"
    app_router: "Modern routing with server components and streaming"
    tailwind: "Utility-first CSS with excellent DX and performance"
    zustand: "Lightweight state management with TypeScript support"
    tanstack_query: "Powerful server state management with caching"
    valibot: "TypeScript-first validation with tree-shaking"
```

### Backend & Database
```yaml
backend_stack:
  api: "Next.js API Routes (App Router)"
  database: "Supabase PostgreSQL"
  authentication: "Supabase Auth"
  file_storage: "Supabase Storage"
  real_time: "Supabase Realtime"
  
  reasoning:
    nextjs_api: "Seamless full-stack development with edge runtime support"
    supabase: "Complete BaaS with PostgreSQL, Auth, Storage, and Realtime"
    postgresql: "Robust relational database with JSON support"
    row_level_security: "Database-level security for multi-tenant apps"
```

### Development Tools
```yaml
development_tools:
  language: "TypeScript 5.0+"
  package_manager: "pnpm"
  bundler: "Next.js built-in (Turbopack)"
  testing: "Vitest + Testing Library + Playwright"
  linting: "ESLint + Prettier + TypeScript ESLint"
  
  reasoning:
    typescript: "Type safety and excellent developer experience"
    pnpm: "Fastest package manager with efficient disk usage"
    turbopack: "Next-generation bundler for faster builds"
    vitest: "Fast unit testing with native TypeScript support"
    playwright: "Reliable cross-browser E2E testing"
```

### Deployment & Infrastructure
```yaml
deployment_stack:
  hosting: "Vercel"
  database: "Supabase Cloud"
  cdn: "Vercel Edge Network"
  monitoring: "Vercel Analytics + Sentry"
  domain: "Vercel Domains or custom"
  
  reasoning:
    vercel: "Optimal Next.js deployment with edge functions"
    supabase_cloud: "Managed PostgreSQL with global distribution"
    edge_network: "Global CDN for optimal performance"
    vercel_analytics: "Privacy-focused web analytics"
    sentry: "Comprehensive error tracking and performance monitoring"
```

## 🎯 **Architecture Patterns**

### Application Structure
```yaml
recommended_structure:
  pages: "app/ directory (App Router)"
  components: "components/ with barrel exports"
  utilities: "lib/ for shared utilities"
  types: "types/ for TypeScript definitions"
  styles: "styles/ for global CSS"
  public: "public/ for static assets"
  
  component_organization:
    - "components/ui/ for reusable UI components"
    - "components/forms/ for form components"
    - "components/layout/ for layout components"
    - "components/features/ for feature-specific components"
```

### Data Flow Architecture
```yaml
data_architecture:
  server_components:
    use_for: "Data fetching, initial rendering"
    pattern: "Fetch data at component level"
    caching: "React cache() for expensive operations"
  
  client_components:
    use_for: "Interactivity, browser APIs, state"
    pattern: "Minimal client boundaries"
    state: "Zustand for complex state, useState for simple"
  
  api_layer:
    pattern: "RESTful API routes in app/api/"
    validation: "Valibot schemas on client and server"
    error_handling: "Centralized error handling"
    authentication: "Supabase Auth with RLS"
```

## 🔧 **Configuration Standards**

### TypeScript Configuration
```json
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["dom", "dom.iterable", "ES6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/lib/*": ["./src/lib/*"],
      "@/types/*": ["./src/types/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### Next.js Configuration
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    typedRoutes: true,
    serverComponentsExternalPackages: ['@prisma/client']
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**'
      }
    ]
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY
  }
}

module.exports = nextConfig
```

### Tailwind Configuration
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
      },
      fontFamily: {
        sans: ['var(--font-geist-sans)'],
        mono: ['var(--font-geist-mono)'],
      },
    },
  },
  plugins: [require('@tailwindcss/typography')],
}
```

## 📦 **Package Dependencies**

### Core Dependencies
```json
{
  "dependencies": {
    "next": "^15.0.0",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "@supabase/supabase-js": "^2.45.0",
    "@supabase/ssr": "^0.5.0",
    "zustand": "^5.0.0",
    "@tanstack/react-query": "^5.56.0",
    "valibot": "^0.42.0",
    "tailwindcss": "^3.4.0",
    "geist": "^1.3.0"
  },
  "devDependencies": {
    "typescript": "^5.6.0",
    "@types/react": "^18.3.0",
    "@types/react-dom": "^18.3.0",
    "vitest": "^2.1.0",
    "@testing-library/react": "^16.0.0",
    "@testing-library/jest-dom": "^6.5.0",
    "playwright": "^1.48.0",
    "eslint": "^8.57.0",
    "eslint-config-next": "^15.0.0",
    "@typescript-eslint/eslint-plugin": "^8.8.0",
    "prettier": "^3.3.0",
    "prettier-plugin-tailwindcss": "^0.6.0"
  }
}
```

### Alternative Stack Considerations
```yaml
alternative_stacks:
  remix:
    when_to_use: "When you need more control over the server/client boundary"
    tradeoffs: "More complex setup, but more explicit data loading"
  
  t3_stack:
    when_to_use: "When you need tRPC for type-safe APIs"
    components: "Next.js + tRPC + Prisma + NextAuth.js"
  
  astro:
    when_to_use: "Content-heavy sites with minimal interactivity"
    benefits: "Excellent performance, islands architecture"
  
  sveltekit:
    when_to_use: "When bundle size is critical"
    benefits: "Smaller runtime, excellent DX"
```

## 🌐 **Deployment Preferences**

### Vercel Deployment (Recommended)
```yaml
vercel_setup:
  framework_preset: "Next.js"
  node_version: "20.x"
  build_command: "next build"
  output_directory: ".next"
  install_command: "pnpm install"
  
  environment_variables:
    - NEXT_PUBLIC_SUPABASE_URL
    - NEXT_PUBLIC_SUPABASE_ANON_KEY
    - SUPABASE_SERVICE_ROLE_KEY
    - DATABASE_URL
  
  edge_functions:
    use_for: "API routes that need global distribution"
    limitations: "Limited Node.js APIs, smaller bundle size"
```

### Alternative Deployment Options
```yaml
alternative_deployment:
  netlify:
    good_for: "JAMstack sites with form handling"
    edge_functions: "Deno-based edge functions"
  
  cloudflare_pages:
    good_for: "Global distribution with Workers integration"
    limitations: "Some Node.js compatibility issues"
  
  aws_amplify:
    good_for: "Integration with other AWS services"
    complexity: "More complex setup and configuration"
  
  railway:
    good_for: "Full-stack apps with databases"
    benefits: "Simple deployment with database included"
```

## 🔐 **Security Preferences**

### Authentication Strategy
```yaml
auth_preferences:
  primary: "Supabase Auth"
  providers: ["Email/Password", "Google OAuth", "GitHub OAuth"]
  session_management: "JWT with automatic refresh"
  security_features:
    - "Row Level Security (RLS)"
    - "Email verification"
    - "Password reset flows"
    - "Rate limiting"
```

### Security Headers
```yaml
security_headers:
  content_security_policy: "strict-dynamic with nonces"
  x_frame_options: "DENY"
  x_content_type_options: "nosniff"
  referrer_policy: "strict-origin-when-cross-origin"
  permissions_policy: "restrictive"
  strict_transport_security: "max-age=31536000; includeSubDomains; preload"
```

## 📊 **Performance Preferences**

### Core Web Vitals Targets
```yaml
performance_targets:
  largest_contentful_paint: "<2.5s"
  first_input_delay: "<100ms"
  cumulative_layout_shift: "<0.1"
  first_contentful_paint: "<1.8s"
  time_to_interactive: "<3.8s"
  
  optimization_strategies:
    - "Server Components for data fetching"
    - "Dynamic imports for code splitting"
    - "Next.js Image optimization"
    - "Edge caching strategies"
    - "Font optimization with next/font"
```

This tech stack preferences file provides the Architect agent with opinionated but flexible technology choices that prioritize developer experience, performance, and maintainability.
