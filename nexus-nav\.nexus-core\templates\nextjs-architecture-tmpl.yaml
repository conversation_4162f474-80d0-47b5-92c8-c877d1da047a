title: "Next.js Architecture Document"
description: "Template for Next.js 15+ App Router architecture with React Server Components"
version: "1.0.0"
framework: "NEXUS"

sections:
  - id: overview
    title: "Architecture Overview"
    instruction: "Provide high-level Next.js application architecture"
    template: |
      # {{app_name}} Next.js Architecture
      
      ## Application Overview
      {{app_overview}}
      
      ## Tech Stack
      - **Framework**: Next.js 15+ (App Router)
      - **React**: React 19 (Server Components)
      - **TypeScript**: 5.8+
      - **Styling**: Tailwind CSS 4.1.11
      - **State Management**: Zustand 5+ (Client), TanStack Query v5 (Server State)
      - **Validation**: Valibot v1.1.0
      - **Database**: Supabase (PostgreSQL + Auth + Storage + Realtime)
      
      ## Scale Requirements
      {{scale_requirements}}

  - id: app_router_structure
    title: "App Router Structure"
    instruction: "Define the app directory structure and routing strategy"
    template: |
      ## App Router Structure
      
      ```
      app/
      ├── (auth)/                 # Route groups for auth pages
      │   ├── login/
      │   └── register/
      ├── (dashboard)/            # Protected dashboard routes
      │   ├── dashboard/
      │   ├── settings/
      │   └── profile/
      ├── api/                    # API routes
      │   ├── auth/
      │   ├── users/
      │   └── data/
      ├── globals.css             # Global styles
      ├── layout.tsx              # Root layout
      ├── loading.tsx             # Global loading UI
      ├── error.tsx               # Global error UI
      ├── not-found.tsx           # 404 page
      └── page.tsx                # Home page
      ```
      
      ## Routing Strategy
      {{routing_strategy}}
      
      ## Route Groups
      {{route_groups}}

  - id: server_components
    title: "Server Components Architecture"
    instruction: "Define Server Components strategy and data fetching"
    template: |
      ## Server Components Strategy
      
      ### Default Approach
      - **Server Components by default** for all components
      - **Client Components only when needed** for interactivity
      - **Data fetching at component level** using async/await
      
      ### Data Fetching Patterns
      ```typescript
      // Server Component with data fetching
      export default async function {{component_name}}() {
        const data = await fetch('{{api_endpoint}}', {
          cache: 'force-cache', // or 'no-store' for dynamic data
        });
        
        return <div>{/* Render data */}</div>;
      }
      ```
      
      ### Caching Strategy
      {{caching_strategy}}
      
      ### Streaming and Suspense
      {{streaming_suspense}}

  - id: client_components
    title: "Client Components Strategy"
    instruction: "Define when and how to use Client Components"
    template: |
      ## Client Components Strategy
      
      ### When to Use Client Components
      {{client_component_usage}}
      
      ### State Management
      ```typescript
      // Zustand store for client state
      interface {{store_interface}} {
        {{store_properties}}
      }
      
      export const use{{store_name}} = create<{{store_interface}}>((set) => ({
        {{store_implementation}}
      }));
      ```
      
      ### Event Handling
      {{event_handling}}
      
      ### Browser APIs
      {{browser_apis}}

  - id: api_routes
    title: "API Routes Architecture"
    instruction: "Define API routes structure and implementation"
    template: |
      ## API Routes Architecture
      
      ### Route Structure
      ```
      app/api/
      ├── auth/
      │   ├── login/route.ts
      │   ├── logout/route.ts
      │   └── refresh/route.ts
      ├── users/
      │   ├── route.ts              # GET /api/users, POST /api/users
      │   └── [id]/route.ts         # GET, PUT, DELETE /api/users/[id]
      └── data/
          ├── route.ts
          └── [...slug]/route.ts    # Catch-all routes
      ```
      
      ### Route Handlers
      ```typescript
      // app/api/{{endpoint}}/route.ts
      export async function GET(request: Request) {
        {{get_implementation}}
      }
      
      export async function POST(request: Request) {
        {{post_implementation}}
      }
      ```
      
      ### Edge Runtime Usage
      {{edge_runtime_usage}}

  - id: data_layer
    title: "Data Layer Architecture"
    instruction: "Define data fetching and state management strategy"
    template: |
      ## Data Layer Architecture
      
      ### Supabase Integration
      ```typescript
      // lib/supabase/client.ts
      import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
      
      export const supabase = createClientComponentClient();
      ```
      
      ### Server-Side Data Fetching
      {{server_data_fetching}}
      
      ### Client-Side Data Fetching (TanStack Query)
      ```typescript
      // hooks/use{{entity}}.ts
      export function use{{entity}}() {
        return useQuery({
          queryKey: ['{{entity}}'],
          queryFn: () => {{fetch_function}},
        });
      }
      ```
      
      ### Real-time Subscriptions
      {{realtime_subscriptions}}

  - id: styling_architecture
    title: "Styling Architecture"
    instruction: "Define Tailwind CSS 4.1.11 and component styling strategy"
    template: |
      ## Styling Architecture
      
      ### Tailwind CSS 4.1.11 Configuration
      ```typescript
      // tailwind.config.ts
      import type { Config } from 'tailwindcss';
      
      const config: Config = {
        {{tailwind_config}}
      };
      
      export default config;
      ```
      
      ### Component Styling Strategy
      {{component_styling}}
      
      ### Design System Integration
      {{design_system}}
      
      ### Responsive Design
      {{responsive_design}}

  - id: performance
    title: "Performance Optimization"
    instruction: "Define performance optimization strategies for millions of users"
    template: |
      ## Performance Optimization
      
      ### Bundle Optimization
      {{bundle_optimization}}
      
      ### Image Optimization
      ```typescript
      import Image from 'next/image';
      
      <Image
        src="{{image_src}}"
        alt="{{image_alt}}"
        width={{width}}
        height={{height}}
        priority={{{priority}}}
      />
      ```
      
      ### Caching Strategy
      {{caching_strategy}}
      
      ### Core Web Vitals Optimization
      {{core_web_vitals}}

  - id: security
    title: "Security Implementation"
    instruction: "Define security measures for SaaS application"
    template: |
      ## Security Implementation
      
      ### Authentication Flow
      {{authentication_flow}}
      
      ### Authorization Patterns
      {{authorization_patterns}}
      
      ### Security Headers
      ```typescript
      // next.config.js
      const nextConfig = {
        async headers() {
          return [
            {
              source: '/(.*)',
              headers: {{security_headers}},
            },
          ];
        },
      };
      ```
      
      ### Input Validation
      {{input_validation}}

placeholders:
  app_name: "Application name"
  app_overview: "High-level application description and purpose"
  scale_requirements: "Expected scale and performance requirements"
  routing_strategy: "How routes are organized and structured"
  route_groups: "Route group organization and purpose"
  component_name: "Server component name"
  api_endpoint: "API endpoint for data fetching"
  caching_strategy: "Data caching and revalidation strategy"
  streaming_suspense: "Streaming and Suspense implementation"
  client_component_usage: "When and why to use Client Components"
  store_interface: "TypeScript interface for Zustand store"
  store_properties: "Store state properties"
  store_name: "Zustand store name"
  store_implementation: "Store implementation with actions"
  event_handling: "Event handling patterns"
  browser_apis: "Browser API usage patterns"
  endpoint: "API endpoint name"
  get_implementation: "GET request handler implementation"
  post_implementation: "POST request handler implementation"
  edge_runtime_usage: "When and how to use Edge Runtime"
  server_data_fetching: "Server-side data fetching patterns"
  entity: "Data entity name"
  fetch_function: "Data fetching function"
  realtime_subscriptions: "Real-time data subscription patterns"
  tailwind_config: "Tailwind CSS configuration"
  component_styling: "Component styling patterns and conventions"
  design_system: "Design system integration approach"
  responsive_design: "Responsive design implementation"
  bundle_optimization: "Bundle size optimization techniques"
  image_src: "Image source path"
  image_alt: "Image alt text"
  width: "Image width"
  height: "Image height"
  priority: "Image loading priority"
  core_web_vitals: "Core Web Vitals optimization strategies"
  authentication_flow: "User authentication implementation"
  authorization_patterns: "Authorization and permission patterns"
  security_headers: "Security headers configuration"
  input_validation: "Input validation and sanitization"

validation:
  required_sections: ["overview", "app_router_structure", "server_components", "api_routes", "performance"]
  nextjs_version: "15+"
  react_version: "19"
  tailwind_version: "4.1.11"
  typescript_required: true
