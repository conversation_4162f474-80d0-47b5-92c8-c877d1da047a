# NEXUS Writing Style Guide

## 📝 **Documentation Writing Standards**

### Voice and Tone

#### Professional but Approachable
```yaml
voice_characteristics:
  professional: "Authoritative and knowledgeable"
  approachable: "Friendly and accessible"
  clear: "Direct and unambiguous"
  helpful: "Solution-oriented and supportive"

tone_guidelines:
  technical_docs: "Precise and detailed"
  user_guides: "Encouraging and supportive"
  api_docs: "Concise and comprehensive"
  tutorials: "Step-by-step and patient"
```

#### Writing Principles
- **Clarity First**: Write for understanding, not to impress
- **User-Centric**: Focus on what the reader needs to accomplish
- **Scannable**: Use headings, lists, and formatting for easy scanning
- **Actionable**: Provide clear next steps and examples
- **Consistent**: Maintain consistent terminology and style

### Language Guidelines

#### Technical Terminology
```yaml
preferred_terms:
  "Next.js App Router": not "Next.js Pages Router"
  "Server Components": not "SSR components"
  "Client Components": not "CSR components"
  "Supabase": not "database" (when specifically referring to <PERSON>pa<PERSON>)
  "TypeScript": not "TS" (in formal documentation)
  "API endpoint": not "API route" (unless specifically Next.js routes)
  "authentication": not "auth" (in headings and formal text)
  "configuration": not "config" (in formal documentation)

consistency_rules:
  capitalization: "Use proper capitalization for product names"
  abbreviations: "Spell out on first use, then use abbreviation"
  code_terms: "Use backticks for code terms in prose"
  file_paths: "Use forward slashes for all file paths"
```

#### Code References
- Use `backticks` for inline code, file names, and technical terms
- Use code blocks for multi-line examples
- Always specify language for syntax highlighting
- Include comments in code examples for clarity

### Structure and Formatting

#### Document Structure
```yaml
standard_structure:
  title: "Clear, descriptive H1"
  overview: "Brief description of purpose and scope"
  prerequisites: "What readers need before starting"
  main_content: "Step-by-step instructions or explanations"
  examples: "Practical examples and code samples"
  troubleshooting: "Common issues and solutions"
  next_steps: "What to do after completing this guide"

heading_hierarchy:
  h1: "Document title only"
  h2: "Major sections"
  h3: "Subsections"
  h4: "Sub-subsections (use sparingly)"
  h5_h6: "Avoid unless absolutely necessary"
```

#### Lists and Formatting
- Use numbered lists for sequential steps
- Use bullet points for non-sequential items
- Use tables for structured data comparison
- Use callouts for important information
- Use code blocks for examples

### Content Guidelines

#### Writing for Different Audiences

##### Developers
```yaml
developer_content:
  assumptions: "Familiar with web development concepts"
  detail_level: "Technical implementation details"
  examples: "Complete, working code examples"
  focus: "How to implement and integrate"

writing_style:
  - Start with the goal or outcome
  - Provide complete code examples
  - Explain the "why" behind technical decisions
  - Include error handling and edge cases
  - Reference related documentation
```

##### End Users
```yaml
user_content:
  assumptions: "May not be technical"
  detail_level: "Step-by-step instructions with screenshots"
  examples: "Real-world scenarios and use cases"
  focus: "How to accomplish tasks"

writing_style:
  - Use simple, clear language
  - Provide visual aids when helpful
  - Anticipate common questions
  - Offer multiple paths to the same goal
  - Include troubleshooting for common issues
```

#### Code Examples

##### Best Practices
```typescript
// ✅ Good: Complete, working example with context
// components/UserProfile.tsx
import { User } from '@/types/user';

interface UserProfileProps {
  user: User;
  onUpdate: (user: User) => void;
}

export function UserProfile({ user, onUpdate }: UserProfileProps) {
  const handleSubmit = async (formData: FormData) => {
    // Handle form submission
    const updatedUser = await updateUser(user.id, formData);
    onUpdate(updatedUser);
  };

  return (
    <form action={handleSubmit}>
      <input name="name" defaultValue={user.name} />
      <button type="submit">Update Profile</button>
    </form>
  );
}
```

```typescript
// ❌ Avoid: Incomplete or unclear examples
export function UserProfile() {
  // ... some code
  return <div>...</div>;
}
```

##### Code Example Guidelines
- Always include necessary imports
- Use realistic variable names and data
- Include error handling when relevant
- Add comments for complex logic
- Show the complete file structure when helpful

### Documentation Types

#### API Documentation
```yaml
api_doc_structure:
  endpoint_description: "What the endpoint does"
  authentication: "Required authentication"
  parameters: "Request parameters with types"
  request_example: "Complete request example"
  response_example: "Complete response example"
  error_responses: "Possible error responses"
  rate_limiting: "Rate limiting information"

example_format: |
  ### POST /api/users
  
  Creates a new user account.
  
  **Authentication**: Required (Bearer token)
  
  **Parameters**:
  - `email` (string, required): User's email address
  - `name` (string, required): User's full name
  - `role` (string, optional): User role (default: 'user')
  
  **Request Example**:
  ```typescript
  const response = await fetch('/api/users', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer your-token'
    },
    body: JSON.stringify({
      email: '<EMAIL>',
      name: 'John Doe',
      role: 'admin'
    })
  });
  ```
```

#### Tutorial Documentation
```yaml
tutorial_structure:
  introduction: "What you'll build and learn"
  prerequisites: "Required knowledge and setup"
  steps: "Numbered, sequential steps"
  verification: "How to verify each step worked"
  troubleshooting: "Common issues and solutions"
  conclusion: "Summary and next steps"

step_format: |
  ## Step 1: Set Up the Project
  
  In this step, you'll create a new Next.js project and install the required dependencies.
  
  1. Create a new Next.js project:
     ```bash
     npx create-next-app@latest my-app --typescript --tailwind --app
     ```
  
  2. Navigate to the project directory:
     ```bash
     cd my-app
     ```
  
  3. Install additional dependencies:
     ```bash
     npm install @supabase/supabase-js
     ```
  
  **Verification**: You should see a new directory called `my-app` with the Next.js project structure.
```

### Quality Standards

#### Content Review Checklist
- [ ] Clear, descriptive title
- [ ] Accurate and up-to-date information
- [ ] Complete code examples that work
- [ ] Proper grammar and spelling
- [ ] Consistent terminology
- [ ] Appropriate level of detail for audience
- [ ] Working links and references
- [ ] Proper formatting and structure

#### Accessibility Guidelines
- Use descriptive link text (not "click here")
- Provide alt text for images
- Use proper heading hierarchy
- Ensure sufficient color contrast
- Write clear, simple language
- Structure content logically

### Maintenance and Updates

#### Keeping Documentation Current
- Review documentation quarterly
- Update examples when dependencies change
- Verify all links and references
- Update screenshots and visual aids
- Gather feedback from users
- Track documentation usage and effectiveness

#### Version Control for Documentation
- Use semantic versioning for major documentation updates
- Maintain changelog for documentation changes
- Archive outdated versions
- Provide migration guides for breaking changes

This style guide ensures consistent, high-quality documentation across all NEXUS framework materials.
