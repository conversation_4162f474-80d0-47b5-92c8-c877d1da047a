version: 2.0.0
markdownExploder: true

# NEXUS Framework Configuration
nexus:
  # Context recall mechanism - prevents hallucination after 50 tool calls
  context_recall:
    enabled: true
    trigger_count: 50
    auto_preserve: true
    include_rules: true
    include_instructions: true
    include_context: true
    include_progress: true

  # Agent coordination and orchestration
  orchestration:
    enabled: true
    autonomous_mode: true
    planning_required: true
    clarification_enabled: true
    quality_gates: true

  # Performance optimization
  performance:
    context_caching: true
    agent_optimization: true
    memory_management: true
prd:
  prdFile: docs/prd.md
  prdVersion: v1
  prdSharded: false
  prdShardedLocation: docs/prd
architecture:
  architectureFile: docs/architecture.md
  architectureVersion: v1
  architectureSharded: false
  architectureShardedLocation: docs/architecture
devLoadAlwaysFiles:
  - docs/architecture/tech-stack.md
  - docs/architecture/coding-standards.md
  - docs/architecture/security-guidelines.md
devDebugLog: .nexus/debug-log.md
devStoryLocation: docs/stories
slashPrefix: Nexus
framework:
  name: NEXUS
  type: micro-agent-system
  techStack:
    - Next.js 15.4+
    - React 19
    - TypeScript 5.8+
    - Supabase
    - Zustand 5+
    - TanStack Query v5
    - Valibot v1.1.0
    - Tailwind CSS 4.0+
    - Shadcn/ui
agents:
  core:
    - analyzer
    - architect
    - implementer
    - validator
    - optimizer
    - documenter
