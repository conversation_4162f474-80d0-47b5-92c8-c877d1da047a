# NEXUS Testing Standards

## 🧪 **Comprehensive Testing Strategy**

### Testing Pyramid Standards
```yaml
testing_levels:
  unit_tests:
    coverage_target: 80%
    focus: "Individual functions and components"
    tools: [vitest, '@testing-library/react']
    location: "__tests__/ or *.test.ts files"
  
  integration_tests:
    coverage_target: 60%
    focus: "Component interactions and API endpoints"
    tools: [vitest, '@testing-library/react', 'msw']
    location: "__tests__/integration/"
  
  e2e_tests:
    coverage_target: 40%
    focus: "Critical user journeys"
    tools: [playwright]
    location: "e2e/"
  
  visual_tests:
    coverage_target: 30%
    focus: "UI regression detection"
    tools: [playwright, chromatic]
    location: "__tests__/visual/"
```

### Test Quality Standards
```yaml
test_quality:
  naming_convention:
    pattern: "describe what it should do when condition"
    example: "should create user when valid data provided"
    avoid: "test 1", "user test", "it works"
  
  test_structure:
    arrange: "Set up test data and conditions"
    act: "Execute the function/interaction being tested"
    assert: "Verify the expected outcome"
  
  assertions:
    specific: "Test exact expected behavior"
    meaningful: "Clear error messages on failure"
    isolated: "Each test stands alone"
    deterministic: "Same input always produces same output"
```

## ⚛️ **React Component Testing Patterns**

### Component Testing Standards
```typescript
// NEXUS Testing Pattern: Component with Props
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { UserProfile } from '@/components/UserProfile'

describe('UserProfile Component', () => {
  const mockUser = {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: 'https://example.com/avatar.jpg'
  }
  
  it('should display user information correctly', () => {
    render(<UserProfile user={mockUser} />)
    
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByRole('img', { name: /john doe/i })).toHaveAttribute(
      'src',
      'https://example.com/avatar.jpg'
    )
  })
  
  it('should call onEdit when edit button is clicked', async () => {
    const mockOnEdit = vi.fn()
    
    render(<UserProfile user={mockUser} onEdit={mockOnEdit} />)
    
    const editButton = screen.getByRole('button', { name: /edit/i })
    fireEvent.click(editButton)
    
    await waitFor(() => {
      expect(mockOnEdit).toHaveBeenCalledWith(mockUser.id)
    })
  })
  
  it('should show loading state when isLoading is true', () => {
    render(<UserProfile user={mockUser} isLoading={true} />)
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
  })
  
  it('should handle missing avatar gracefully', () => {
    const userWithoutAvatar = { ...mockUser, avatar: null }
    
    render(<UserProfile user={userWithoutAvatar} />)
    
    const avatar = screen.getByRole('img', { name: /john doe/i })
    expect(avatar).toHaveAttribute('src', '/default-avatar.png')
  })
})
```

### Form Testing Pattern
```typescript
// NEXUS Testing Pattern: Form Validation and Submission
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi } from 'vitest'
import { CreateUserForm } from '@/components/CreateUserForm'

describe('CreateUserForm', () => {
  const mockOnSubmit = vi.fn()
  
  beforeEach(() => {
    mockOnSubmit.mockClear()
  })
  
  it('should submit form with valid data', async () => {
    const user = userEvent.setup()
    
    render(<CreateUserForm onSubmit={mockOnSubmit} />)
    
    // Fill form
    await user.type(screen.getByLabelText(/name/i), 'John Doe')
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.selectOptions(screen.getByLabelText(/role/i), 'admin')
    
    // Submit
    await user.click(screen.getByRole('button', { name: /create user/i }))
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin'
      })
    })
  })
  
  it('should show validation errors for invalid email', async () => {
    const user = userEvent.setup()
    
    render(<CreateUserForm onSubmit={mockOnSubmit} />)
    
    await user.type(screen.getByLabelText(/email/i), 'invalid-email')
    await user.click(screen.getByRole('button', { name: /create user/i }))
    
    await waitFor(() => {
      expect(screen.getByText(/invalid email address/i)).toBeInTheDocument()
    })
    
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })
  
  it('should disable submit button while submitting', async () => {
    const user = userEvent.setup()
    
    // Mock slow submission
    mockOnSubmit.mockImplementation(() => new Promise(resolve => {
      setTimeout(resolve, 1000)
    }))
    
    render(<CreateUserForm onSubmit={mockOnSubmit} />)
    
    await user.type(screen.getByLabelText(/name/i), 'John Doe')
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    
    const submitButton = screen.getByRole('button', { name: /create user/i })
    await user.click(submitButton)
    
    expect(submitButton).toBeDisabled()
    expect(screen.getByText(/creating.../i)).toBeInTheDocument()
  })
})
```

## 🌐 **API Testing Patterns**

### API Route Testing
```typescript
// NEXUS Testing Pattern: API Route Testing
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { NextRequest, NextResponse } from 'next/server'
import { GET, POST } from '@/app/api/users/route'
import { createMocks } from 'node-mocks-http'

// Mock Supabase client
vi.mock('@/lib/supabase/server', () => ({
  createClient: vi.fn(() => ({
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn()
        }))
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn()
        }))
      }))
    })),
    auth: {
      getUser: vi.fn()
    }
  }))
}))

describe('/api/users', () => {
  describe('GET', () => {
    it('should return paginated users', async () => {
      const request = new NextRequest('http://localhost/api/users?page=1&limit=10')
      
      const response = await GET(request)
      const data = await response.json()
      
      expect(response.status).toBe(200)
      expect(data).toHaveProperty('data')
      expect(data).toHaveProperty('pagination')
      expect(data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: expect.any(Number)
      })
    })
    
    it('should handle invalid page parameter', async () => {
      const request = new NextRequest('http://localhost/api/users?page=invalid')
      
      const response = await GET(request)
      
      // Should default to page 1
      expect(response.status).toBe(200)
    })
  })
  
  describe('POST', () => {
    it('should create user with valid data', async () => {
      const validUserData = {
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'user'
      }
      
      const request = new NextRequest('http://localhost/api/users', {
        method: 'POST',
        body: JSON.stringify(validUserData)
      })
      
      const response = await POST(request)
      const data = await response.json()
      
      expect(response.status).toBe(201)
      expect(data.data).toMatchObject(validUserData)
    })
    
    it('should return 400 for invalid email', async () => {
      const invalidUserData = {
        name: 'John Doe',
        email: 'invalid-email',
        role: 'user'
      }
      
      const request = new NextRequest('http://localhost/api/users', {
        method: 'POST',
        body: JSON.stringify(invalidUserData)
      })
      
      const response = await POST(request)
      const data = await response.json()
      
      expect(response.status).toBe(400)
      expect(data.error).toContain('Validation failed')
    })
    
    it('should return 401 for unauthenticated request', async () => {
      // Mock unauthenticated user
      vi.mocked(createClient).mockReturnValue({
        auth: {
          getUser: vi.fn().mockResolvedValue({ data: { user: null }, error: null })
        }
      } as any)
      
      const request = new NextRequest('http://localhost/api/users', {
        method: 'POST',
        body: JSON.stringify({ name: 'Test', email: '<EMAIL>' })
      })
      
      const response = await POST(request)
      
      expect(response.status).toBe(401)
    })
  })
})
```

### Database Testing Pattern
```typescript
// NEXUS Testing Pattern: Database Operations
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { createClient } from '@supabase/supabase-js'
import { createUser, getUserById, updateUser, deleteUser } from '@/lib/database/users'

describe('User Database Operations', () => {
  let testUserId: string
  
  beforeEach(async () => {
    // Create test user
    const testUser = await createUser({
      name: 'Test User',
      email: '<EMAIL>',
      role: 'user'
    })
    testUserId = testUser.id
  })
  
  afterEach(async () => {
    // Cleanup test data
    if (testUserId) {
      await deleteUser(testUserId)
    }
  })
  
  describe('createUser', () => {
    it('should create user with valid data', async () => {
      const userData = {
        name: 'New User',
        email: '<EMAIL>',
        role: 'admin' as const
      }
      
      const user = await createUser(userData)
      
      expect(user).toMatchObject(userData)
      expect(user.id).toBeDefined()
      expect(user.created_at).toBeDefined()
      
      // Cleanup
      await deleteUser(user.id)
    })
    
    it('should throw error for duplicate email', async () => {
      const userData = {
        name: 'Duplicate User',
        email: '<EMAIL>', // Same as test user
        role: 'user' as const
      }
      
      await expect(createUser(userData)).rejects.toThrow('Email already exists')
    })
  })
  
  describe('getUserById', () => {
    it('should return user for valid ID', async () => {
      const user = await getUserById(testUserId)
      
      expect(user).toBeDefined()
      expect(user.id).toBe(testUserId)
      expect(user.email).toBe('<EMAIL>')
    })
    
    it('should return null for non-existent ID', async () => {
      const user = await getUserById('non-existent-id')
      
      expect(user).toBeNull()
    })
  })
  
  describe('updateUser', () => {
    it('should update user successfully', async () => {
      const updates = { name: 'Updated Name' }
      
      const updatedUser = await updateUser(testUserId, updates)
      
      expect(updatedUser.name).toBe('Updated Name')
      expect(updatedUser.email).toBe('<EMAIL>') // Unchanged
    })
    
    it('should throw error for invalid ID', async () => {
      await expect(
        updateUser('invalid-id', { name: 'New Name' })
      ).rejects.toThrow('User not found')
    })
  })
})
```

## 🎭 **End-to-End Testing Patterns**

### E2E Test Structure
```typescript
// NEXUS Testing Pattern: E2E with Playwright
import { test, expect } from '@playwright/test'

test.describe('User Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    // Wait for dashboard to load
    await expect(page.locator('[data-testid="dashboard"]')).toBeVisible()
  })
  
  test('should create new user successfully', async ({ page }) => {
    // Navigate to users page
    await page.click('[data-testid="users-nav"]')
    await expect(page.locator('h1')).toContainText('Users')
    
    // Click create user button
    await page.click('[data-testid="create-user-button"]')
    
    // Fill form
    await page.fill('[data-testid="user-name"]', 'John Doe')
    await page.fill('[data-testid="user-email"]', '<EMAIL>')
    await page.selectOption('[data-testid="user-role"]', 'user')
    
    // Submit form
    await page.click('[data-testid="submit-button"]')
    
    // Verify success
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    await expect(page.locator('[data-testid="user-list"]')).toContainText('John Doe')
  })
  
  test('should show validation errors for invalid data', async ({ page }) => {
    await page.click('[data-testid="users-nav"]')
    await page.click('[data-testid="create-user-button"]')
    
    // Submit empty form
    await page.click('[data-testid="submit-button"]')
    
    // Check validation errors
    await expect(page.locator('[data-testid="name-error"]')).toContainText('Name is required')
    await expect(page.locator('[data-testid="email-error"]')).toContainText('Email is required')
  })
  
  test('should edit existing user', async ({ page }) => {
    await page.click('[data-testid="users-nav"]')
    
    // Click edit button for first user
    await page.click('[data-testid="edit-user-button"]')
    
    // Update name
    await page.fill('[data-testid="user-name"]', 'Updated Name')
    await page.click('[data-testid="submit-button"]')
    
    // Verify update
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
    await expect(page.locator('[data-testid="user-list"]')).toContainText('Updated Name')
  })
})
```

## 🔧 **Test Utilities and Helpers**

### Test Setup Utilities
```typescript
// NEXUS Testing Pattern: Test Utilities
import { render, RenderOptions } from '@testing-library/react'
import { ReactElement } from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ThemeProvider } from '@/components/ThemeProvider'

// Custom render function with providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })
  
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        {children}
      </ThemeProvider>
    </QueryClientProvider>
  )
}

export const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

// Mock data factories
export const createMockUser = (overrides = {}) => ({
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
  role: 'user',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
})

export const createMockPost = (overrides = {}) => ({
  id: '1',
  title: 'Test Post',
  content: 'Test content',
  author_id: '1',
  published: true,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
})

// API mocking utilities
export const mockAPIResponse = (data: any, status = 200) => {
  return {
    ok: status >= 200 && status < 300,
    status,
    json: async () => data,
    text: async () => JSON.stringify(data)
  }
}

export const mockAPIError = (message: string, status = 500) => {
  return {
    ok: false,
    status,
    json: async () => ({ error: message }),
    text: async () => JSON.stringify({ error: message })
  }
}
```

### Performance Testing
```typescript
// NEXUS Testing Pattern: Performance Testing
import { test, expect } from '@playwright/test'

test.describe('Performance Tests', () => {
  test('should meet Core Web Vitals standards', async ({ page }) => {
    // Navigate to page
    await page.goto('/')
    
    // Measure performance metrics
    const metrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lcp = entries.find(entry => entry.entryType === 'largest-contentful-paint')
          const fid = entries.find(entry => entry.entryType === 'first-input')
          const cls = entries.find(entry => entry.entryType === 'layout-shift')
          
          resolve({
            lcp: lcp?.startTime,
            fid: fid?.processingStart - fid?.startTime,
            cls: cls?.value
          })
        }).observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] })
      })
    })
    
    // Assert performance standards
    expect(metrics.lcp).toBeLessThan(2500) // LCP < 2.5s
    expect(metrics.fid).toBeLessThan(100)  // FID < 100ms
    expect(metrics.cls).toBeLessThan(0.1)  // CLS < 0.1
  })
  
  test('should load page within 3 seconds', async ({ page }) => {
    const startTime = Date.now()
    
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    const loadTime = Date.now() - startTime
    expect(loadTime).toBeLessThan(3000)
  })
})
```

This testing standards file provides the Validator agent with comprehensive testing patterns and quality standards for ensuring reliable, maintainable code.
