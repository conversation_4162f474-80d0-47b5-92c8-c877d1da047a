# NEXUS Framework 🚀

**Micro-Agent Development Framework**  
*Pure Markdown/YAML Approach - No Dependencies*

## 🎯 What is NEXUS?

NEXUS is a specialized micro-agent development framework following the BMAD methodology. It provides intelligent agents with YAML-driven templates for **Next.js + React + TypeScript + Supabase** development. No Node.js dependencies - just copy framework files to your project.

## ⚡ Key Features

- **🤖 6 Specialized Micro-Agents**: <PERSON><PERSON><PERSON>, Architect, Implementer, Validator, Optimizer, Documenter
- **📋 YAML-Driven Templates**: Interactive document generation with elicitation methods
- **🔧 Multi-IDE Support**: Pre-configured for Cursor, GitHub Copilot, Claude Code, and more
- **📁 Pure File-Based**: No installations, just copy `.nexus-core/` folder to projects
- **🎯 Task-Based Workflows**: Modular tasks with dependencies and checklists

## 🛠️ Your Tech Stack

```yaml
Primary Stack:
  - Next.js 15.4+ (App Router)
  - React 19 (Server Components)
  - TypeScript 5.8+
  - Supabase (PostgreSQL + Auth + Storage)

State & Data:
  - Zustand 5+ (Client State)
  - TanStack Query v5 (Server State)
  - Valibot v1.1.0 (Validation)
  - Tailwind CSS 4.0+
  - Shadcn/ui Components
```

## 🚀 Quick Start

### 1. Copy Framework Files

Copy the NEXUS framework to your project:

```bash
# Copy framework files to your project
cp -r /path/to/nexus-nav/.nexus-core ./
```

### 2. Copy IDE Configuration

Choose your IDE and copy the appropriate rules:

#### Cursor IDE:
```bash
cp -r /path/to/nexus-nav/.cursor ./
```

#### GitHub Copilot:
```bash
cp -r /path/to/nexus-nav/.github ./
```

#### Claude Code:
```bash
cp -r /path/to/nexus-nav/.claude ./
```

### 3. Start Using Agents

Activate agents in your IDE chat:

- **@analyzer** - Code quality and security analysis
- **@architect** - System design and PRD creation
- **@implementer** - Feature implementation
- **@validator** - Testing and validation
- **@optimizer** - Performance optimization
- **@documenter** - Documentation creation

## 🤖 Micro-Agents

### 🔍 ANALYZER (Alex)
**Triggers**: `@analyzer`  
**Commands**: `*audit`, `*security`, `*performance`, `*dependencies`  
**Purpose**: Code quality assessment, security scanning, performance analysis

### 🏗️ ARCHITECT (Aria)
**Triggers**: `@architect`  
**Commands**: `*create-doc`, `*design`, `*api-design`, `*database`  
**Purpose**: System design, architecture documents, PRD creation

### ⚡ IMPLEMENTER (Ivan)
**Triggers**: `@implementer`  
**Commands**: `*component`, `*api`, `*feature`, `*refactor`  
**Purpose**: Feature implementation, component generation, code creation

### ✅ VALIDATOR (Vera)
**Triggers**: `@validator`  
**Commands**: `*test`, `*security`, `*accessibility`, `*review`  
**Purpose**: Testing, validation, quality assurance

### 🚀 OPTIMIZER (Otto)
**Triggers**: `@optimizer`  
**Commands**: `*performance`, `*bundle`, `*database`, `*caching`  
**Purpose**: Performance optimization, resource efficiency

### 📚 DOCUMENTER (Diana)
**Triggers**: `@documenter`  
**Commands**: `*create-docs`, `*api-docs`, `*readme`, `*setup-guide`  
**Purpose**: Documentation creation, technical writing

## 📋 Common Workflows

### Create a PRD
```
1. Type: @architect
2. Agent greets you as Aria
3. Type: *create-doc prd-tmpl
4. Follow interactive prompts
5. PRD saved to docs/prd.md
```

### Code Analysis
```
1. Type: @analyzer
2. Agent greets you as Alex
3. Type: *audit
4. Comprehensive code analysis provided
```

### Implement Feature
```
1. Type: @implementer
2. Agent greets you as Ivan
3. Type: *feature
4. Follow implementation guidance
```

## 📁 Framework Structure

```
your-project/
├── .nexus-core/                 # Framework files (copied)
│   ├── agents/                  # Agent definitions
│   │   ├── analyzer.md
│   │   ├── architect.md
│   │   ├── implementer.md
│   │   ├── validator.md
│   │   ├── optimizer.md
│   │   └── documenter.md
│   ├── tasks/                   # Task instructions
│   │   ├── create-prd.md
│   │   ├── code-audit.md
│   │   └── ...
│   ├── templates/               # YAML templates
│   │   ├── prd-tmpl.yaml
│   │   ├── architecture-tmpl.yaml
│   │   └── ...
│   ├── checklists/              # Quality checklists
│   ├── data/                    # Reference data
│   └── core-config.yaml         # Framework config
├── .cursor/                     # Cursor IDE rules (if using Cursor)
│   └── rules/
│       ├── nexus-framework.mdc
│       ├── analyzer.mdc
│       └── ...
├── .github/                     # GitHub Copilot (if using VS Code)
│   └── copilot/
│       └── instructions/
└── docs/                        # Generated documents
    ├── prd.md
    ├── architecture.md
    └── ...
```

## 🎯 Agent Commands

All agents support these patterns:

- **`*help`** - Show agent-specific commands
- **`*exit`** - Exit agent mode
- **Agent-specific commands** - See individual agent documentation

### ARCHITECT Commands
- `*create-doc {template}` - Generate documents from templates
- `*design` - System architecture design
- `*api-design` - API architecture planning
- `*database` - Database design
- `*research {topic}` - Architectural research

### IMPLEMENTER Commands
- `*component` - Create React components
- `*api` - Create API routes
- `*feature` - Implement complete features
- `*hook` - Create custom hooks
- `*refactor` - Code refactoring

### VALIDATOR Commands
- `*test` - Generate test cases
- `*security` - Security validation
- `*accessibility` - A11y testing
- `*integration` - Integration tests
- `*e2e` - End-to-end tests

## 📊 Templates

NEXUS uses YAML-driven templates for consistent document generation:

### PRD Template (`prd-tmpl.yaml`)
- Goals and Background Context
- Requirements (Functional/Non-functional)
- Technical Architecture
- UI/UX Requirements
- Security Requirements
- Performance Requirements
- Testing Strategy
- Deployment Requirements

### Architecture Template (`architecture-tmpl.yaml`)
- System Overview
- Component Architecture
- Data Flow
- API Design
- Database Schema
- Security Architecture
- Performance Considerations
- Deployment Strategy

## 🔧 Development Standards

### Code Quality
- Use TypeScript strict mode
- Implement proper error handling
- Follow Next.js 15 App Router patterns
- Use React 19 Server Components appropriately
- Implement proper loading and error states

### Security
- Validate all inputs with Valibot
- Implement Supabase RLS policies
- Use proper authentication flows
- Sanitize user-generated content
- Follow OWASP guidelines

### Performance
- Implement proper caching strategies
- Use React optimization hooks appropriately
- Optimize images with Next.js Image component
- Minimize bundle size with tree shaking
- Use Suspense boundaries for loading states

### Testing
- Write unit tests for business logic
- Include integration tests for API routes
- Test React components with Testing Library
- Validate TypeScript types
- Include E2E tests for critical paths

## 🎨 IDE Integration

### Cursor IDE
- Rules in `.cursor/rules/` directory
- Agent activation with `@agent-name`
- Commands with `*command` format
- File references with `mdc:` links

### GitHub Copilot (VS Code)
- Instructions in `.github/copilot/instructions/`
- Custom instructions per agent
- Command-based workflows
- Context-aware assistance

### Claude Code
- Configuration in `.claude/` directory
- Agent-based chat modes
- Template-driven generation
- Project-specific customization

## 🤝 Contributing

NEXUS follows the BMAD methodology:

1. All agents are defined in pure markdown with YAML metadata
2. Templates use YAML structure with interactive elicitation
3. Tasks are markdown files with clear instructions
4. No Node.js dependencies - pure file-based approach
5. IDE integration through native rule systems

## 📖 Documentation

- **Agent Definitions**: `.nexus-core/agents/`
- **Task Instructions**: `.nexus-core/tasks/`
- **Templates**: `.nexus-core/templates/`
- **Checklists**: `.nexus-core/checklists/`
- **Reference Data**: `.nexus-core/data/`

## 🚀 Next Steps

1. Copy `.nexus-core/` to your project
2. Copy IDE-specific rules for your environment
3. Start with `@architect` and create your first PRD
4. Use specialized agents for development tasks
5. Customize templates and workflows as needed

---

**NEXUS Framework** - Micro-Agent Development Made Simple ✨
