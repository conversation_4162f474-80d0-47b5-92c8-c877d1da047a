title: "API Design Document"
description: "Template for designing RESTful APIs with Next.js and Supabase"
version: "1.0.0"
framework: "NEXUS"

sections:
  - id: overview
    title: "API Overview"
    instruction: "Provide high-level API description and purpose"
    template: |
      # {{api_name}} API Design
      
      ## Purpose
      {{api_purpose}}
      
      ## Target Users
      {{target_users}}
      
      ## Key Features
      {{key_features}}

  - id: authentication
    title: "Authentication & Authorization"
    instruction: "Define authentication strategy using Supabase Auth"
    template: |
      ## Authentication Strategy
      
      ### Authentication Method
      - **Provider**: Supabase Auth
      - **Token Type**: JWT with automatic refresh
      - **Session Management**: Server-side validation
      
      ### Authorization Levels
      {{authorization_levels}}
      
      ### Row Level Security (RLS)
      {{rls_policies}}

  - id: endpoints
    title: "API Endpoints"
    instruction: "Define all API endpoints with Next.js App Router"
    repeatable: true
    template: |
      ### {{method}} {{endpoint_path}}
      
      **Purpose**: {{endpoint_purpose}}
      
      **Authentication**: {{auth_required}}
      
      **Request**:
      ```typescript
      // Path: app/api{{endpoint_path}}/route.ts
      interface {{request_interface}} {
        {{request_schema}}
      }
      ```
      
      **Response**:
      ```typescript
      interface {{response_interface}} {
        {{response_schema}}
      }
      ```
      
      **Implementation Notes**:
      {{implementation_notes}}

  - id: data_models
    title: "Data Models"
    instruction: "Define TypeScript interfaces and Supabase schemas"
    template: |
      ## Data Models
      
      ### TypeScript Interfaces
      ```typescript
      {{typescript_interfaces}}
      ```
      
      ### Supabase Schema
      ```sql
      {{supabase_schema}}
      ```
      
      ### Validation Schemas (Valibot)
      ```typescript
      {{valibot_schemas}}
      ```

  - id: error_handling
    title: "Error Handling"
    instruction: "Define error response patterns and status codes"
    template: |
      ## Error Handling
      
      ### Standard Error Response
      ```typescript
      interface APIError {
        error: string;
        message: string;
        code: number;
        details?: any;
      }
      ```
      
      ### Status Codes
      {{status_codes}}
      
      ### Error Scenarios
      {{error_scenarios}}

  - id: performance
    title: "Performance Considerations"
    instruction: "Define caching, rate limiting, and optimization strategies"
    template: |
      ## Performance Optimization
      
      ### Caching Strategy
      {{caching_strategy}}
      
      ### Rate Limiting
      {{rate_limiting}}
      
      ### Database Optimization
      {{db_optimization}}
      
      ### Edge Runtime Usage
      {{edge_runtime_usage}}

  - id: security
    title: "Security Measures"
    instruction: "Define security implementation for millions of users"
    template: |
      ## Security Implementation
      
      ### Input Validation
      {{input_validation}}
      
      ### SQL Injection Prevention
      {{sql_injection_prevention}}
      
      ### CORS Configuration
      {{cors_configuration}}
      
      ### Security Headers
      {{security_headers}}

  - id: testing
    title: "Testing Strategy"
    instruction: "Define API testing approach"
    template: |
      ## Testing Strategy
      
      ### Unit Tests
      {{unit_tests}}
      
      ### Integration Tests
      {{integration_tests}}
      
      ### Load Testing
      {{load_testing}}
      
      ### Security Testing
      {{security_testing}}

placeholders:
  api_name: "API name (e.g., User Management API)"
  api_purpose: "Primary purpose and business value"
  target_users: "Who will use this API"
  key_features: "Main API capabilities"
  authorization_levels: "User roles and permissions"
  rls_policies: "Row Level Security policies"
  method: "HTTP method (GET, POST, PUT, DELETE)"
  endpoint_path: "API endpoint path"
  endpoint_purpose: "What this endpoint does"
  auth_required: "Authentication requirements"
  request_interface: "TypeScript interface name for request"
  request_schema: "Request body/params schema"
  response_interface: "TypeScript interface name for response"
  response_schema: "Response body schema"
  implementation_notes: "Special implementation considerations"
  typescript_interfaces: "All TypeScript type definitions"
  supabase_schema: "Database table definitions"
  valibot_schemas: "Validation schemas using Valibot"
  status_codes: "HTTP status codes used"
  error_scenarios: "Common error cases and handling"
  caching_strategy: "How to implement caching"
  rate_limiting: "Rate limiting implementation"
  db_optimization: "Database query optimization"
  edge_runtime_usage: "When to use Edge Runtime"
  input_validation: "Input validation strategy"
  sql_injection_prevention: "SQL injection prevention measures"
  cors_configuration: "CORS setup for API"
  security_headers: "Required security headers"
  unit_tests: "Unit testing approach"
  integration_tests: "Integration testing strategy"
  load_testing: "Load testing for scalability"
  security_testing: "Security testing procedures"

validation:
  required_sections: ["overview", "authentication", "endpoints", "data_models", "error_handling"]
  tech_stack_compliance: true
  security_review: true
  performance_review: true
