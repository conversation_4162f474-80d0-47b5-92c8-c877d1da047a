# NEXUS Installation Guide

## 📁 Manual Installation (Recommended)

NEXUS follows the BMAD methodology - pure file-based approach with no dependencies.

### Step 1: Copy Framework Files

Copy the `.nexus-core` directory to your project:

```bash
# From the nexus-nav framework directory
cp -r .nexus-core /path/to/your-project/

# Or on Windows
xcopy .nexus-core C:\path\to\your-project\.nexus-core /E /I
```

### Step 2: Choose Your IDE Configuration

Copy the appropriate IDE configuration:

#### For Cursor IDE:
```bash
cp -r .cursor /path/to/your-project/
```

#### For GitHub Copilot (VS Code):
```bash
cp -r .github /path/to/your-project/
```

#### For Claude Code:
```bash
cp -r .claude /path/to/your-project/
```

### Step 3: Verify Installation

Your project should now have:

```
your-project/
├── .nexus-core/          # ✅ Framework files
│   ├── agents/
│   ├── tasks/
│   ├── templates/
│   └── core-config.yaml
├── .cursor/              # ✅ IDE rules (if using Cursor)
│   └── rules/
├── .github/              # ✅ Copilot instructions (if using VS Code)
│   └── copilot/
└── .claude/              # ✅ Claude instructions (if using Claude Code)
    └── instructions.md
```

## 🚀 Quick Test

### Test Agent Activation

1. Open your IDE chat
2. Type: `@architect`
3. You should see: "🏗️ Hello! I'm Aria, your System Architect..."
4. Type: `*help`
5. You should see a list of available commands

### Test PRD Generation

1. Activate architect: `@architect`
2. Create PRD: `*create-doc prd-tmpl`
3. Follow the interactive prompts
4. Check that `docs/prd.md` is created

## 🎯 IDE-Specific Setup

### Cursor IDE Setup

1. Copy `ide-configs/cursor/.cursor/` folder to your project root
2. Rules are automatically active for `.mdc` files
3. Type `@architect` in chat to test
4. Use `*help` to see commands

### GitHub Copilot Setup

1. Copy `ide-configs/github-copilot/.github/` folder to your project root
2. Chatmodes are in `.github/chatmodes/`
3. Instructions are in `.github/instructions/`
4. Restart VS Code to load instructions
5. Type `@architect` in GitHub Copilot chat

### Claude Code Setup

1. Copy `ide-configs/claude-code/.claude/` folder to your project root
2. Instructions are in `.claude/instructions.md`
3. Agent activation works with `@agent-name`
4. Commands use `*command` format

### Augment IDE Setup

1. Copy `ide-configs/augment/.augment/` folder to your project root
2. Agent files are ready for Augment's agent system
3. Type `@architect` to activate agents
4. Use `*help` to see available commands

### Trae IDE Setup

1. Copy `ide-configs/trae/.trae/` folder to your project root
2. Agent configurations are optimized for Trae
3. Type `@architect` to activate agents
4. Use `*help` to see available commands

## 🔧 Framework Structure

### Core Files (Always Required)

```
.nexus-core/
├── core-config.yaml     # Framework configuration
├── agents/              # Agent definitions
│   ├── analyzer.md      # Alex - Code analysis
│   ├── architect.md     # Aria - System design
│   ├── implementer.md   # Ivan - Implementation
│   ├── validator.md     # Vera - Testing/QA
│   ├── optimizer.md     # Otto - Performance
│   └── documenter.md    # Diana - Documentation
├── tasks/               # Task instructions
│   ├── create-prd.md    # PRD generation
│   ├── code-audit.md    # Code analysis
│   └── ...
├── templates/           # YAML templates
│   ├── prd-tmpl.yaml    # PRD template
│   ├── architecture-tmpl.yaml
│   └── ...
├── checklists/          # Quality checklists
├── data/               # Reference data
└── utils/              # Utility functions
```

### IDE Configuration Files

#### Cursor Rules (`.cursor/rules/`)
- `nexus-framework.mdc` - Main framework rules
- `analyzer.mdc` - Analyzer agent rules
- `architect.mdc` - Architect agent rules
- `implementer.mdc` - Implementer agent rules
- `validator.mdc` - Validator agent rules
- `optimizer.mdc` - Optimizer agent rules
- `documenter.mdc` - Documenter agent rules

#### GitHub Copilot Instructions (`.github/copilot/instructions/`)
- `nexus-framework.md` - Main framework instructions
- `analyzer.md` - Analyzer agent instructions
- `architect.md` - Architect agent instructions
- Individual agent instruction files

#### Claude Code Configuration (`.claude/`)
- `instructions.md` - Main framework instructions
- Agent activation and command patterns

## 🎨 Customization

### Adding Custom Templates

1. Create YAML file in `.nexus-core/templates/`
2. Follow existing template structure
3. Reference in agent commands
4. Update agent dependencies

### Creating Custom Tasks

1. Add markdown file in `.nexus-core/tasks/`
2. Follow task instruction format
3. Reference from agent commands
4. Include in agent dependencies

### Modifying Agents

1. Edit agent files in `.nexus-core/agents/`
2. Update commands and dependencies
3. Modify corresponding IDE rules
4. Test agent activation and commands

## 🔍 Troubleshooting

### Agent Not Activating

1. **Check file paths**: Ensure `.nexus-core/` is in project root
2. **Verify IDE rules**: Confirm IDE-specific files are copied
3. **Restart IDE**: Some IDEs need restart to load new rules
4. **Check syntax**: Ensure `@agent-name` format is correct

### Commands Not Working

1. **Use asterisk prefix**: Commands need `*` prefix (e.g., `*help`)
2. **Check agent state**: Make sure agent is activated first
3. **Verify dependencies**: Ensure referenced files exist
4. **Check file permissions**: Ensure files are readable

### Templates Not Loading

1. **Check YAML syntax**: Validate template YAML structure
2. **Verify file paths**: Ensure templates are in `.nexus-core/templates/`
3. **Check references**: Verify agent dependencies list templates correctly
4. **Validate template ID**: Ensure unique template IDs

### IDE-Specific Issues

#### Cursor IDE
- Ensure `.mdc` files are in `.cursor/rules/`
- Check that `alwaysApply: false` for agent rules
- Verify main framework rule has `alwaysApply: true`

#### GitHub Copilot
- Ensure instructions are in `.github/copilot/instructions/`
- Restart VS Code after adding instructions
- Check that custom instructions are enabled

#### Claude Code
- Ensure instructions are in `.claude/instructions.md`
- Verify agent activation patterns work
- Check command format is correct

## 📚 Next Steps

1. **Read agent documentation** in `.nexus-core/agents/`
2. **Review templates** in `.nexus-core/templates/`
3. **Explore tasks** in `.nexus-core/tasks/`
4. **Try example workflows** from main README
5. **Customize for your project** needs

## 🆘 Support

If you encounter issues:

1. Check this installation guide thoroughly
2. Verify file structure matches examples
3. Test with simple agent activation
4. Review agent documentation files
5. Ensure IDE-specific setup is correct

The NEXUS framework is designed to be simple and dependency-free. If something isn't working, it's usually a file path or IDE configuration issue.
