# NEXUS API Patterns

## 🌐 **Next.js App Router API Patterns**

### Route Structure Patterns
```typescript
// NEXUS Pattern: RESTful API Route Structure
// app/api/users/route.ts - Collection operations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    
    const users = await getUsersPaginated({ page, limit })
    
    return NextResponse.json({
      data: users,
      pagination: { page, limit, total: users.length }
    })
  } catch (error) {
    return handleAPIError(error)
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = CreateUserSchema.parse(body)
    
    const user = await createUser(validatedData)
    
    return NextResponse.json({ data: user }, { status: 201 })
  } catch (error) {
    return handleAPIError(error)
  }
}

// app/api/users/[id]/route.ts - Resource operations
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getUserById(params.id)
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    return NextResponse.json({ data: user })
  } catch (error) {
    return handleAPIError(error)
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const validatedData = UpdateUserSchema.parse(body)
    
    const user = await updateUser(params.id, validatedData)
    
    return NextResponse.json({ data: user })
  } catch (error) {
    return handleAPIError(error)
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await deleteUser(params.id)
    
    return NextResponse.json(
      { message: 'User deleted successfully' },
      { status: 200 }
    )
  } catch (error) {
    return handleAPIError(error)
  }
}
```

### Authentication Middleware Pattern
```typescript
// NEXUS Pattern: Authentication Wrapper
export function withAuth<T extends Record<string, unknown>>(
  handler: (
    request: NextRequest,
    context: T & { user: User }
  ) => Promise<NextResponse>
) {
  return async (request: NextRequest, context: T) => {
    try {
      const supabase = createClient()
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error || !user) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }
      
      // Add user to context
      return handler(request, { ...context, user })
    } catch (error) {
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: 401 }
      )
    }
  }
}

// Usage:
export const GET = withAuth(async (request, { params, user }) => {
  // User is guaranteed to be authenticated
  const data = await getProtectedData(user.id)
  return NextResponse.json({ data })
})
```

### Authorization Pattern
```typescript
// NEXUS Pattern: Role-Based Authorization
export function withRole(requiredRole: Role) {
  return function <T extends Record<string, unknown>>(
    handler: (
      request: NextRequest,
      context: T & { user: User }
    ) => Promise<NextResponse>
  ) {
    return withAuth(async (request, context) => {
      const { user } = context
      
      // Check user role
      if (!hasRole(user, requiredRole)) {
        return NextResponse.json(
          { error: 'Insufficient permissions' },
          { status: 403 }
        )
      }
      
      return handler(request, context)
    })
  }
}

// Usage:
export const DELETE = withRole('admin')(async (request, { params, user }) => {
  // Only admins can access this endpoint
  await deleteResource(params.id)
  return NextResponse.json({ success: true })
})
```

## 🔐 **Security Patterns**

### Input Validation Pattern
```typescript
// NEXUS Pattern: Comprehensive Input Validation
import { object, string, number, email, parse } from 'valibot'

const CreatePostSchema = object({
  title: string([
    minLength(1, 'Title is required'),
    maxLength(200, 'Title too long')
  ]),
  content: string([
    minLength(10, 'Content must be at least 10 characters'),
    maxLength(10000, 'Content too long')
  ]),
  categoryId: string([uuid('Invalid category ID')]),
  tags: array(string([maxLength(50, 'Tag too long')]), [
    maxLength(10, 'Too many tags')
  ]),
  published: boolean()
})

export async function POST(request: NextRequest) {
  try {
    // Parse and validate input
    const body = await request.json()
    const validatedData = parse(CreatePostSchema, body)
    
    // Additional business logic validation
    const categoryExists = await categoryExists(validatedData.categoryId)
    if (!categoryExists) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      )
    }
    
    // Sanitize HTML content
    const sanitizedContent = sanitizeHTML(validatedData.content)
    
    const post = await createPost({
      ...validatedData,
      content: sanitizedContent
    })
    
    return NextResponse.json({ data: post }, { status: 201 })
  } catch (error) {
    return handleAPIError(error)
  }
}
```

### Rate Limiting Pattern
```typescript
// NEXUS Pattern: API Rate Limiting
import { Redis } from 'ioredis'

const redis = new Redis(process.env.REDIS_URL!)

interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Max requests per window
  keyGenerator: (request: NextRequest) => string
}

export function withRateLimit(config: RateLimitConfig) {
  return function <T extends Record<string, unknown>>(
    handler: (request: NextRequest, context: T) => Promise<NextResponse>
  ) {
    return async (request: NextRequest, context: T) => {
      const key = `rate_limit:${config.keyGenerator(request)}`
      const now = Date.now()
      const windowStart = now - config.windowMs
      
      try {
        // Clean old entries and count current requests
        await redis.zremrangebyscore(key, 0, windowStart)
        const requestCount = await redis.zcard(key)
        
        if (requestCount >= config.maxRequests) {
          return NextResponse.json(
            { error: 'Rate limit exceeded' },
            { status: 429 }
          )
        }
        
        // Add current request
        await redis.zadd(key, now, `${now}-${Math.random()}`)
        await redis.expire(key, Math.ceil(config.windowMs / 1000))
        
        return handler(request, context)
      } catch (error) {
        console.error('Rate limiting error:', error)
        // Continue without rate limiting on Redis errors
        return handler(request, context)
      }
    }
  }
}

// Usage:
export const POST = withRateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100,
  keyGenerator: (request) => {
    const ip = request.headers.get('x-forwarded-for') || 'unknown'
    return `api:${ip}`
  }
})(withAuth(async (request, { user }) => {
  // Rate-limited authenticated endpoint
  const data = await processRequest(user.id)
  return NextResponse.json({ data })
}))
```

## 📊 **Data Fetching Patterns**

### Database Query Optimization
```typescript
// NEXUS Pattern: Efficient Database Queries
export async function getPostsWithStats(userId: string) {
  const supabase = createClient()
  
  // Single query with aggregations instead of multiple queries
  const { data, error } = await supabase
    .from('posts')
    .select(`
      id,
      title,
      content,
      published,
      created_at,
      updated_at,
      author:users!posts_author_id_fkey (
        id,
        name,
        avatar_url
      ),
      comments:comments!comments_post_id_fkey (count),
      likes:post_likes!post_likes_post_id_fkey (count),
      tags:post_tags!post_tags_post_id_fkey (
        tag:tags!post_tags_tag_id_fkey (
          id,
          name
        )
      )
    `)
    .eq('author_id', userId)
    .eq('published', true)
    .order('created_at', { ascending: false })
    .limit(20)
  
  if (error) {
    throw new APIError('Failed to fetch posts', 500)
  }
  
  return data
}
```

### Caching Pattern
```typescript
// NEXUS Pattern: Response Caching
import { NextRequest, NextResponse } from 'next/server'

const cache = new Map<string, { data: any; timestamp: number }>()
const CACHE_TTL = 5 * 60 * 1000 // 5 minutes

export function withCache(ttl: number = CACHE_TTL) {
  return function <T extends Record<string, unknown>>(
    handler: (request: NextRequest, context: T) => Promise<NextResponse>
  ) {
    return async (request: NextRequest, context: T) => {
      const cacheKey = `${request.method}:${request.url}`
      const cached = cache.get(cacheKey)
      
      // Return cached response if valid
      if (cached && Date.now() - cached.timestamp < ttl) {
        const response = NextResponse.json(cached.data)
        response.headers.set('X-Cache', 'HIT')
        return response
      }
      
      // Execute handler and cache result
      const response = await handler(request, context)
      
      if (response.ok) {
        const data = await response.clone().json()
        cache.set(cacheKey, { data, timestamp: Date.now() })
        
        // Clean old cache entries periodically
        if (Math.random() < 0.01) { // 1% chance
          cleanCache()
        }
      }
      
      response.headers.set('X-Cache', 'MISS')
      return response
    }
  }
}

function cleanCache() {
  const now = Date.now()
  for (const [key, value] of cache.entries()) {
    if (now - value.timestamp > CACHE_TTL) {
      cache.delete(key)
    }
  }
}

// Usage:
export const GET = withCache(10 * 60 * 1000)(async (request) => {
  const data = await getExpensiveData()
  return NextResponse.json({ data })
})
```

## 🔄 **Error Handling Patterns**

### Comprehensive Error Handler
```typescript
// NEXUS Pattern: Centralized Error Handling
export class APIError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string,
    public details?: any
  ) {
    super(message)
    this.name = 'APIError'
  }
}

export function handleAPIError(error: unknown): NextResponse {
  // Log error for monitoring
  console.error('API Error:', {
    error,
    stack: error instanceof Error ? error.stack : undefined,
    timestamp: new Date().toISOString()
  })
  
  // Validation errors
  if (error instanceof ValiError) {
    return NextResponse.json(
      {
        error: 'Validation failed',
        details: error.issues.map(issue => ({
          field: issue.path?.map(p => p.key).join('.'),
          message: issue.message
        }))
      },
      { status: 400 }
    )
  }
  
  // Custom API errors
  if (error instanceof APIError) {
    return NextResponse.json(
      {
        error: error.message,
        code: error.code,
        details: error.details
      },
      { status: error.statusCode }
    )
  }
  
  // Database errors
  if (error instanceof Error && error.message.includes('duplicate key')) {
    return NextResponse.json(
      { error: 'Resource already exists' },
      { status: 409 }
    )
  }
  
  // Authentication/Authorization errors
  if (error instanceof Error && error.message.includes('auth')) {
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 401 }
    )
  }
  
  // Generic server error
  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  )
}
```

### Retry Pattern with Exponential Backoff
```typescript
// NEXUS Pattern: Resilient External API Calls
export async function callExternalAPI<T>(
  url: string,
  options: RequestInit = {},
  maxRetries: number = 3
): Promise<T> {
  let lastError: Error
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        }
      })
      
      if (!response.ok) {
        throw new APIError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status
        )
      }
      
      return await response.json()
    } catch (error) {
      lastError = error as Error
      
      // Don't retry on client errors (4xx)
      if (error instanceof APIError && error.statusCode < 500) {
        throw error
      }
      
      // Wait before retrying (exponential backoff)
      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000 // 1s, 2s, 4s
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }
  
  throw new APIError(
    `Failed after ${maxRetries + 1} attempts: ${lastError.message}`,
    500
  )
}
```

## 📡 **Real-time Patterns**

### WebSocket API Pattern
```typescript
// NEXUS Pattern: WebSocket Integration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { message, roomId } = body
    
    // Validate and process message
    const validatedMessage = MessageSchema.parse(message)
    
    // Save to database
    const savedMessage = await saveMessage(roomId, validatedMessage)
    
    // Broadcast to all clients in room
    await broadcastToRoom(roomId, {
      type: 'NEW_MESSAGE',
      data: savedMessage
    })
    
    return NextResponse.json({ data: savedMessage })
  } catch (error) {
    return handleAPIError(error)
  }
}

// Helper function for real-time broadcasting
async function broadcastToRoom(roomId: string, data: any) {
  const supabase = createClient()
  
  // Use Supabase real-time to broadcast
  await supabase.channel(`room:${roomId}`).send({
    type: 'broadcast',
    event: 'message',
    payload: data
  })
}
```

This API patterns file provides the Implementer agent with comprehensive patterns for building secure, scalable, and maintainable API endpoints with Next.js App Router.
