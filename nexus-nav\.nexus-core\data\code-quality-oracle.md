# NEXUS Code Quality Oracle

## 🎯 **Production-Ready Code Standards**

Unlike generic frameworks that produce code needing heavy revision, NEXUS enforces production-ready standards from the start.

### TypeScript Excellence
```yaml
typescript_standards:
  strict_mode: enforced
  no_implicit_any: required
  exhaustive_deps: validated
  return_types: explicit
  
  patterns_enforced:
    - "Interfaces over types for extensibility"
    - "Generic constraints for type safety"
    - "Discriminated unions for state management"
    - "Template literal types for string validation"
```

### Next.js 15+ Optimization Patterns
```yaml
nextjs_patterns:
  app_router:
    - "Server Components by default, Client Components when needed"
    - "Loading.tsx for consistent loading states"
    - "Error.tsx for error boundaries"
    - "Not-found.tsx for 404 handling"
  
  performance:
    - "Dynamic imports for code splitting"
    - "Next.js Image for automatic optimization"
    - "Metadata API for SEO optimization"
    - "Static generation where possible"
  
  routing:
    - "Parallel routes for complex layouts"
    - "Intercepting routes for modals"
    - "Route groups for organization"
    - "Dynamic routes with type safety"
```

### React 19 Server Component Mastery
```yaml
react_patterns:
  server_components:
    - "Data fetching in Server Components"
    - "Direct database queries (via Supabase)"
    - "No useState or useEffect in RSC"
    - "Async/await for data operations"
  
  client_components:
    - "'use client' directive when needed"
    - "Event handlers and interactivity"
    - "Browser APIs and state management"
    - "Proper hydration considerations"
  
  composition:
    - "Server as container, Client as leaf"
    - "Pass data down, pass functions up"
    - "Suspense boundaries for loading"
    - "Error boundaries for crashes"
```

### Supabase Integration Excellence
```yaml
supabase_patterns:
  database:
    - "Row Level Security (RLS) mandatory"
    - "XID primary keys for performance"
    - "Proper indexing strategies"
    - "Foreign key constraints"
  
  auth:
    - "Server-side auth validation"
    - "JWT token handling"
    - "Protected routes pattern"
    - "Role-based access control"
  
  real_time:
    - "Subscription cleanup on unmount"
    - "Optimistic updates with rollback"
    - "Conflict resolution strategies"
    - "Connection management"
```

### Security-First Development
```yaml
security_standards:
  input_validation:
    - "Valibot schemas on client and server"
    - "Sanitization before database storage"
    - "Type coercion with validation"
    - "Error message security (no info leaks)"
  
  authentication:
    - "Server-side session validation"
    - "CSRF protection enabled"
    - "Secure cookie configuration"
    - "Rate limiting on auth endpoints"
  
  authorization:
    - "RLS policies for all tables"
    - "Principle of least privilege"
    - "Role-based permissions"
    - "Resource-level access control"
```

## 🚀 **Automatic Code Generation Standards**

### Component Generation Template
```typescript
// NEXUS Generated Component
import { Suspense } from 'react'
import { Metadata } from 'next'

// Server Component (default)
interface {{ComponentName}}Props {
  // Props with explicit types
}

export const metadata: Metadata = {
  title: '{{title}}',
  description: '{{description}}',
}

export default async function {{ComponentName}}({ 
  ...props 
}: {{ComponentName}}Props) {
  // Server-side data fetching
  const data = await fetchData()
  
  return (
    <div className="{{tailwind-classes}}">
      <Suspense fallback={<{{ComponentName}}Loading />}>
        {/* Content */}
      </Suspense>
    </div>
  )
}

// Separate loading component
function {{ComponentName}}Loading() {
  return <div className="animate-pulse">Loading...</div>
}
```

### API Route Generation Template
```typescript
// NEXUS Generated API Route
import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { {{schema}}Schema } from '@/lib/validations'

export async function POST(request: NextRequest) {
  try {
    // Authentication check
    const supabase = createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Input validation
    const body = await request.json()
    const validated = {{schema}}Schema.parse(body)

    // Business logic with RLS
    const { data, error } = await supabase
      .from('{{table}}')
      .insert(validated)
      .select()

    if (error) {
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json({ error: 'Internal error' }, { status: 500 })
  }
}
```

### Database Schema Generation
```sql
-- NEXUS Generated Schema
CREATE TABLE {{table_name}} (
  id TEXT PRIMARY KEY DEFAULT generate_xid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Business fields with proper types
  {{fields}}
  
  -- Audit fields
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Indexes for performance
  INDEX idx_{{table_name}}_user_id ON {{table_name}}(user_id),
  INDEX idx_{{table_name}}_created_at ON {{table_name}}(created_at)
);

-- RLS Policies
ALTER TABLE {{table_name}} ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can manage their own {{table_name}}" ON {{table_name}}
  FOR ALL USING (user_id = auth.uid());
```

## 🔍 **Quality Validation Engine**

### Automated Code Review Checklist
```yaml
code_review_automation:
  type_safety:
    - "No 'any' types used"
    - "All function parameters typed"
    - "Return types specified"
    - "Props interfaces defined"
  
  security:
    - "All inputs validated with Valibot"
    - "RLS policies applied"
    - "No hardcoded secrets"
    - "Auth checks on protected routes"
  
  performance:
    - "Server Components for data fetching"
    - "Dynamic imports for code splitting"
    - "Images optimized with Next.js Image"
    - "Suspense boundaries for loading"
  
  accessibility:
    - "Semantic HTML elements"
    - "ARIA labels where needed"
    - "Keyboard navigation support"
    - "Color contrast compliance"
```

This is what makes NEXUS **10000x better** - it doesn't just provide templates, it enforces **production-ready standards** that eliminate the need for heavy manual review and fixes!
