# NEXUS Framework Instructions for Claude Code

This file provides core instructions for the NEXUS micro-agent system when working with Claude Code.

## System Overview

NEXUS is a revolutionary micro-agent framework that provides 6 specialized agents for complete software development lifecycle management. Each agent is a domain expert with access to comprehensive knowledge bases and production-ready patterns.

## Available Agents

### @analyzer - Code Analysis & Security Expert
- **Role**: Senior Code Analysis Expert & Security Auditor
- **Focus**: Code quality assessment, security vulnerability scanning, performance analysis
- **When to use**: Code quality assessment, security scanning, dependency analysis, performance bottleneck identification

### @architect - System Design & Architecture Expert
- **Role**: Enterprise Solution Architect & Technical Lead
- **Focus**: System architecture design, component structure planning, technology stack decisions
- **When to use**: System architecture design, technology stack decisions, integration patterns

### @implementer - Development & Implementation Expert
- **Role**: Senior Full-Stack Developer & Code Craftsman
- **Focus**: Code development, feature building, component creation, integration
- **When to use**: Code development, feature implementation, component creation, refactoring

### @validator - Quality Assurance & Testing Expert
- **Role**: Quality Assurance Engineer & Test Automation Specialist
- **Focus**: Code review, testing strategy, security validation, performance validation
- **When to use**: Code review, testing strategy, quality assurance, validation

### @optimizer - Performance & Optimization Expert
- **Role**: Performance Engineer & Optimization Specialist
- **Focus**: Performance analysis, resource optimization, scalability improvements
- **When to use**: Performance analysis, optimization, scalability improvements, monitoring

### @documenter - Documentation & Knowledge Expert
- **Role**: Technical Writer & Knowledge Management Specialist
- **Focus**: Technical documentation, API documentation, user guides, knowledge management
- **When to use**: Technical documentation, API documentation, user guides, tutorials

## Core Framework Features

### 1. Context Intelligence Engine
- Maintains context continuity across agent interactions
- References comprehensive knowledge bases in .nexus-core/data/
- Intelligent pattern recognition and application

### 2. Production-Ready Code Standards
- Enforces coding standards from .nexus-core/data/coding-standards.md
- Applies security patterns from .nexus-core/data/security-patterns.md
- Implements performance patterns from .nexus-core/data/performance-patterns.md

### 3. Comprehensive Knowledge Base
- Security patterns and best practices
- Performance optimization techniques
- Component architecture patterns
- API design patterns
- Testing standards and methodologies
- Tech stack preferences and configurations
- Documentation standards

## Usage Workflow

1. **Analysis Phase**: Start with @analyzer for requirements and analysis
2. **Design Phase**: Use @architect for system design and architecture
3. **Implementation Phase**: Use @implementer for development
4. **Validation Phase**: Use @validator for quality assurance and testing
5. **Optimization Phase**: Use @optimizer for performance improvements
6. **Documentation Phase**: Use @documenter for comprehensive documentation

## File Structure

```
.nexus-core/
├── agents/           # Agent definitions
├── tasks/            # Task instructions
├── templates/        # YAML templates
├── checklists/       # Quality checklists
└── data/            # Knowledge base files
    ├── security-patterns.md
    ├── performance-patterns.md
    ├── coding-standards.md
    ├── component-patterns.md
    ├── api-patterns.md
    ├── testing-standards.md
    ├── tech-stack-preferences.md
    └── documentation-standards.md
```

## Quality Standards

All agents follow comprehensive quality standards:
- TypeScript strict mode with explicit types
- React Server Components with Next.js 15+
- Supabase for backend services
- Tailwind CSS for styling
- Comprehensive security measures
- Performance optimization
- Thorough testing coverage
- Complete documentation

## Getting Started

1. Choose the appropriate agent for your task
2. Use @{agent-name} to activate the agent
3. Use *help to see available commands
4. Follow the agent's guidance and recommendations
5. Switch agents as needed for different phases

The NEXUS framework ensures production-ready code that follows best practices and requires minimal manual fixes.
