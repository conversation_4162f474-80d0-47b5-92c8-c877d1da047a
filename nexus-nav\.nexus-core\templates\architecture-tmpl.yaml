template:
  id: architecture-template-nexus-v1
  name: System Architecture Document - NEXUS Framework
  version: 1.0
  output:
    format: markdown
    filename: docs/architecture.md
    title: "{{project_name}} System Architecture"

workflow:
  mode: interactive
  elicitation: advanced-elicitation

sections:
  - id: overview
    title: System Overview
    instruction: |
      Provide a high-level overview of the system architecture using the NEXUS tech stack. Include the purpose, scope, and key architectural decisions.
    sections:
      - id: purpose
        title: System Purpose
        type: paragraphs
        instruction: 1-2 paragraphs describing what the system does and its primary objectives
      - id: scope
        title: Scope and Boundaries
        type: bullet-list
        instruction: Define what is included and excluded from the system scope
      - id: stakeholders
        title: Key Stakeholders
        type: table
        columns: [Role, Responsibilities, Concerns]
        instruction: Identify key stakeholders and their interests in the architecture

  - id: architecture-principles
    title: Architecture Principles
    instruction: |
      Define the guiding principles for the NEXUS framework architecture decisions.
    sections:
      - id: design-principles
        title: Design Principles
        type: bullet-list
        instruction: |
          Core principles guiding architecture decisions:
        examples:
          - "API-First Design - All features exposed through well-defined APIs"
          - "Security by Design - Security considerations in every architectural decision"
          - "Performance First - Optimize for Core Web Vitals and user experience"
          - "Type Safety - Leverage TypeScript for compile-time error prevention"
      - id: technology-principles
        title: Technology Principles
        type: bullet-list
        instruction: |
          Technology selection and usage principles:
        examples:
          - "Next.js App Router for modern React architecture"
          - "Server Components for optimal performance and SEO"
          - "Supabase for backend-as-a-service with PostgreSQL"
          - "TypeScript strict mode for maximum type safety"

  - id: tech-stack
    title: Technology Stack
    instruction: |
      Detail the complete NEXUS framework technology stack and justify choices.
    elicit: true
    sections:
      - id: frontend-stack
        title: Frontend Architecture
        instruction: |
          Frontend technology choices and architecture:
          - Next.js 15.4+ with App Router
          - React 19 with Server Components
          - TypeScript 5.8+ with strict mode
          - Tailwind CSS 4.0+ for styling
          - Shadcn/ui for component library
      - id: backend-stack
        title: Backend Architecture
        instruction: |
          Backend and data layer architecture:
          - Supabase PostgreSQL database
          - Supabase Auth for authentication
          - Supabase Storage for file management
          - Supabase Edge Functions for serverless compute
          - Row Level Security (RLS) for data protection
      - id: state-management
        title: State Management
        instruction: |
          Client and server state management approach:
          - Zustand 5+ for client-side state
          - TanStack Query v5 for server state and caching
          - React Context for component tree state
          - URL state for shareable application state
      - id: data-validation
        title: Data Validation
        instruction: |
          Input validation and schema management:
          - Valibot v1.1.0 for runtime validation
          - TypeScript for compile-time type checking
          - Client and server-side validation patterns
          - Schema sharing between frontend and backend

  - id: system-architecture
    title: System Architecture
    instruction: |
      Define the overall system architecture and component interactions.
    elicit: true
    sections:
      - id: component-diagram
        title: Component Architecture
        instruction: |
          High-level component architecture showing:
          - Next.js App Router structure
          - React Server Components vs Client Components
          - Supabase service integration
          - External API integrations
      - id: data-flow
        title: Data Flow Architecture
        instruction: |
          How data flows through the system:
          - User interactions and form submissions
          - API route processing
          - Database operations with RLS
          - Real-time subscriptions
          - State management updates
      - id: deployment-architecture
        title: Deployment Architecture
        instruction: |
          Production deployment structure:
          - Vercel hosting for Next.js application
          - Supabase cloud for backend services
          - CDN configuration for static assets
          - Environment management strategy

  - id: database-design
    title: Database Architecture
    condition: Project uses database
    instruction: |
      Design the database architecture using Supabase PostgreSQL.
    elicit: true
    sections:
      - id: schema-design
        title: Database Schema
        instruction: |
          PostgreSQL schema design including:
          - Table structure and relationships
          - Primary keys (preferably XID for performance)
          - Foreign key constraints
          - Indexes for query optimization
      - id: rls-policies
        title: Row Level Security
        instruction: |
          Supabase RLS policy design:
          - User access patterns
          - Data isolation requirements
          - Permission-based access control
          - Security policy implementation
      - id: real-time
        title: Real-time Features
        instruction: |
          Real-time data synchronization:
          - Supabase subscriptions
          - WebSocket connection management
          - Optimistic updates with TanStack Query
          - Conflict resolution strategies

  - id: api-design
    title: API Architecture
    instruction: |
      Define the API architecture using Next.js App Router.
    elicit: true
    sections:
      - id: api-structure
        title: API Route Structure
        instruction: |
          Next.js App Router API organization:
          - Route handler patterns
          - Middleware implementation
          - Error handling strategies
          - Request/response schemas
      - id: authentication
        title: Authentication Architecture
        instruction: |
          Supabase Auth integration:
          - JWT token handling
          - Session management
          - OAuth provider setup
          - Protected route patterns
      - id: validation
        title: API Validation
        instruction: |
          Input validation architecture:
          - Valibot schema definitions
          - Request validation middleware
          - Response validation
          - Error message standardization

  - id: security-architecture
    title: Security Architecture
    instruction: |
      Comprehensive security design for the NEXUS framework.
    elicit: true
    sections:
      - id: authentication-security
        title: Authentication & Authorization
        instruction: |
          Security measures for auth:
          - Supabase Auth configuration
          - JWT token security
          - Session management
          - Role-based access control (RBAC)
      - id: data-protection
        title: Data Protection
        instruction: |
          Data security implementation:
          - Row Level Security policies
          - Data encryption at rest
          - HTTPS enforcement
          - Input sanitization
      - id: application-security
        title: Application Security
        instruction: |
          Frontend and API security:
          - Content Security Policy (CSP)
          - Cross-site scripting (XSS) protection
          - Cross-site request forgery (CSRF) protection
          - Security headers configuration

  - id: performance-architecture
    title: Performance Architecture
    instruction: |
      Performance optimization strategies for the NEXUS stack.
    elicit: true
    sections:
      - id: frontend-performance
        title: Frontend Performance
        instruction: |
          React and Next.js optimization:
          - Server Component optimization
          - Code splitting strategies
          - Image optimization with Next.js Image
          - Bundle size optimization
      - id: backend-performance
        title: Backend Performance
        instruction: |
          Database and API performance:
          - Database query optimization
          - Connection pooling
          - Caching strategies with TanStack Query
          - CDN configuration
      - id: monitoring
        title: Performance Monitoring
        instruction: |
          Performance tracking and alerts:
          - Core Web Vitals monitoring
          - Database performance metrics
          - Error tracking and logging
          - User experience analytics

  - id: scalability
    title: Scalability Considerations
    instruction: |
      How the architecture scales with growth.
    sections:
      - id: horizontal-scaling
        title: Horizontal Scaling
        instruction: Strategies for scaling out the application
      - id: database-scaling
        title: Database Scaling
        instruction: Supabase scaling options and optimization strategies
      - id: caching-strategy
        title: Caching Strategy
        instruction: Multi-level caching approach for performance
      - id: cdn-strategy
        title: CDN Strategy
        instruction: Content delivery network configuration and optimization

metadata:
  framework: NEXUS
  tech_stack_version: "1.0"
  created_by: "NEXUS Framework Generator"
  last_updated: "{{current_date}}"
