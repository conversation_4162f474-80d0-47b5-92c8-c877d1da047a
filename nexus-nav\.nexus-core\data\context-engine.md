# Enhanced Context Engine for NEXUS Framework

## 🧠 **Intelligent Context Management**

### Context Persistence Across Sessions
```yaml
context_memory:
  architectural_decisions:
    - database_schema: "Decided on XID primary keys for performance"
    - authentication: "Using Supabase Auth with RLS policies"
    - state_management: "Zustand for client, TanStack Query for server"
    - validation: "Valibot schemas shared between client/server"
  
  security_requirements:
    - rls_mandatory: true
    - input_validation: valibot_enforced
    - oauth_providers: [google, github]
    - session_management: supabase_jwt
  
  performance_targets:
    - core_web_vitals: passing
    - lighthouse_score: 90+
    - bundle_size: <500kb
    - first_paint: <1.5s
```

### Smart Context Injection
When any agent activates, automatically inject relevant context:

```markdown
🧠 **Context Loaded**:
- Architecture: Next.js App Router with React Server Components
- Database: Supabase PostgreSQL with RLS enabled
- Auth: Supabase Auth with Google/GitHub OAuth
- State: <PERSON>ust<PERSON> (client) + TanStack Query (server)
- Validation: Valibot schemas with TypeScript
- Previous decisions: [See context memory above]
```

## 🔍 **Pattern Recognition Engine**

### Successful Pattern Learning
```yaml
learned_patterns:
  component_patterns:
    - server_component_optimization: "Use RSC for data fetching, client for interactivity"
    - error_boundary_pattern: "Wrap async components with Suspense and ErrorBoundary"
    - loading_state_pattern: "Use loading.tsx files for consistent loading UI"
  
  api_patterns:
    - route_handler_structure: "Consistent error handling and response formatting"
    - middleware_chain: "Auth → Validation → Business Logic → Response"
    - database_pattern: "Use Supabase client with RLS for all operations"
  
  security_patterns:
    - input_sanitization: "Valibot validation on both client and server"
    - rls_policy_template: "User-based access control with performance optimization"
    - auth_flow: "JWT token handling with automatic refresh"
```

### Anti-Pattern Detection
```yaml
avoid_patterns:
  performance_killers:
    - "Client Components fetching data in useEffect"
    - "Missing React.memo for expensive renders"
    - "Unoptimized database queries without indexes"
  
  security_issues:
    - "Direct database access without RLS"
    - "Unvalidated user inputs"
    - "Hardcoded secrets in client code"
  
  maintainability_issues:
    - "Massive components without decomposition"
    - "Shared state without proper boundaries"
    - "Inconsistent error handling patterns"
```

## 🎯 **Quality Enforcement Engine**

### Automatic Code Quality Validation
```yaml
quality_checks:
  typescript_compliance:
    strict_mode: enforced
    no_any_types: required
    exhaustive_deps: enforced
    return_type_annotations: required
  
  security_compliance:
    rls_policies: validated
    input_validation: valibot_required
    auth_checks: mandatory
    xss_protection: enforced
  
  performance_compliance:
    bundle_analysis: automatic
    core_web_vitals: monitored
    database_query_optimization: validated
    image_optimization: required
```

### Production-Ready Standards
```yaml
production_standards:
  error_handling:
    - "All async operations wrapped in try-catch"
    - "User-friendly error messages"
    - "Error boundaries for component crashes"
    - "Logging for debugging and monitoring"
  
  accessibility:
    - "WCAG AA compliance minimum"
    - "Keyboard navigation support"
    - "Screen reader compatibility"
    - "Color contrast validation"
  
  performance:
    - "Lazy loading for non-critical components"
    - "Image optimization with Next.js Image"
    - "Database query optimization"
    - "Proper caching strategies"
```

## 🤝 **Inter-Agent Intelligence**

### Agent Collaboration Protocol
```yaml
agent_communication:
  analyzer_to_architect:
    - "Security findings inform architecture decisions"
    - "Performance bottlenecks guide design choices"
    - "Code quality issues shape patterns"
  
  architect_to_implementer:
    - "Architectural decisions guide implementation patterns"
    - "Security requirements enforce coding standards"
    - "Performance targets influence optimization choices"
  
  implementer_to_validator:
    - "Implementation patterns inform test strategies"
    - "Security measures require specific test coverage"
    - "Performance optimizations need validation"
```

### Shared Knowledge Base
```yaml
shared_knowledge:
  project_context:
    tech_stack: "Next.js 15+ React 19 TypeScript Supabase"
    domain: "{{project_domain}}"
    users: "{{target_users}}"
    scale: "{{expected_scale}}"
  
  decisions_made:
    - architecture_patterns
    - security_requirements
    - performance_targets
    - coding_standards
  
  lessons_learned:
    - successful_implementations
    - avoided_pitfalls
    - optimization_wins
    - security_measures_effective
```
