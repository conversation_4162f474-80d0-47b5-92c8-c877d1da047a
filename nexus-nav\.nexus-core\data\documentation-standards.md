# NEXUS Documentation Standards

## 📚 **Documentation Excellence Standards**

### Documentation Hierarchy
```yaml
documentation_levels:
  user_documentation:
    readme: "Project overview, quick start, basic usage"
    guides: "Step-by-step tutorials and how-tos"
    api_reference: "Complete API documentation"
    troubleshooting: "Common issues and solutions"
  
  developer_documentation:
    architecture: "System design and component relationships"
    code_comments: "Inline explanations for complex logic"
    api_docs: "Endpoint documentation with examples"
    contributing: "Guidelines for contributors"
  
  maintenance_documentation:
    deployment: "Deployment procedures and configurations"
    monitoring: "Health checks and performance metrics"
    backup_recovery: "Data backup and disaster recovery"
    changelog: "Version history and breaking changes"
```

### Writing Standards
```yaml
writing_principles:
  clarity:
    - "Use simple, clear language"
    - "Avoid jargon and technical complexity"
    - "Write for your audience's expertise level"
    - "Use active voice over passive voice"
  
  structure:
    - "Start with overview, then dive into details"
    - "Use consistent headings and formatting"
    - "Include table of contents for long documents"
    - "Break up text with headings, lists, and code blocks"
  
  completeness:
    - "Include all necessary information"
    - "Provide examples for complex concepts"
    - "Link to related documentation"
    - "Keep information up to date"
```

## 📖 **README Standards**

### Essential README Sections
```markdown
# Project Name

Brief description of what the project does and why it exists.

## Features

- ✅ Feature 1 with brief description
- ✅ Feature 2 with brief description
- ✅ Feature 3 with brief description

## Quick Start

### Prerequisites

- Node.js 20+
- pnpm 9+
- PostgreSQL 16+ (or Supabase account)

### Installation

```bash
# Clone the repository
git clone https://github.com/username/project-name.git
cd project-name

# Install dependencies
pnpm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# Run database migrations
pnpm db:migrate

# Start development server
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## Documentation

- [User Guide](./docs/user-guide.md) - How to use the application
- [API Reference](./docs/api-reference.md) - Complete API documentation
- [Architecture](./docs/architecture.md) - System design and components
- [Contributing](./CONTRIBUTING.md) - Guidelines for contributors

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Database**: Supabase (PostgreSQL)
- **Styling**: Tailwind CSS
- **State Management**: Zustand + TanStack Query
- **Authentication**: Supabase Auth
- **Deployment**: Vercel

## Environment Variables

```bash
# Required
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Optional
DATABASE_URL=your_direct_database_url
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Scripts

```bash
pnpm dev          # Start development server
pnpm build        # Build for production
pnpm start        # Start production server
pnpm test         # Run tests
pnpm test:e2e     # Run E2E tests
pnpm lint         # Run ESLint
pnpm type-check   # Run TypeScript checks
```

## Contributing

See [CONTRIBUTING.md](./CONTRIBUTING.md) for guidelines on how to contribute.

## License

This project is licensed under the MIT License - see [LICENSE](./LICENSE) file.
```

## 🔧 **API Documentation Standards**

### API Endpoint Documentation
```markdown
## User Management API

### Create User

Creates a new user account.

**Endpoint**: `POST /api/users`

**Authentication**: Required (Admin role)

**Request Body**:
```json
{
  "name": "string (required, 1-100 chars)",
  "email": "string (required, valid email)",
  "role": "string (required, one of: 'user', 'admin')",
  "avatar": "string (optional, valid URL)"
}
```

**Response**:
```json
{
  "data": {
    "id": "string",
    "name": "string",
    "email": "string",
    "role": "string",
    "avatar": "string | null",
    "created_at": "string (ISO 8601)",
    "updated_at": "string (ISO 8601)"
  }
}
```

**Error Responses**:
- `400 Bad Request`: Invalid input data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Admin role required
- `409 Conflict`: Email already exists
- `500 Internal Server Error`: Server error

**Example**:
```bash
curl -X POST /api/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "user"
  }'
```

### Get User

Retrieves a user by ID.

**Endpoint**: `GET /api/users/{id}`

**Authentication**: Required

**Parameters**:
- `id` (path): User ID (string, required)

**Response**:
```json
{
  "data": {
    "id": "string",
    "name": "string",
    "email": "string",
    "role": "string",
    "avatar": "string | null",
    "created_at": "string (ISO 8601)",
    "updated_at": "string (ISO 8601)"
  }
}
```

**Error Responses**:
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Access denied (can only view own profile unless admin)
- `404 Not Found`: User not found
- `500 Internal Server Error`: Server error
```

## 🏗️ **Architecture Documentation Standards**

### System Architecture Template
```markdown
# System Architecture

## Overview

This document describes the high-level architecture of the application, including component relationships, data flow, and key design decisions.

## Architecture Diagram

```mermaid
graph TD
    A[Client Browser] --> B[Next.js App Router]
    B --> C[API Routes]
    B --> D[Server Components]
    C --> E[Supabase PostgreSQL]
    D --> E
    B --> F[Client Components]
    F --> G[Zustand Store]
    F --> H[TanStack Query]
    H --> C
```

## Component Architecture

### Frontend Layer
- **Next.js App Router**: Handles routing, SSR, and static generation
- **Server Components**: Data fetching and initial rendering
- **Client Components**: Interactive features and client-side state
- **Tailwind CSS**: Utility-first styling system

### API Layer
- **API Routes**: RESTful endpoints in `/app/api/`
- **Authentication**: Supabase Auth with JWT tokens
- **Validation**: Valibot schemas for input validation
- **Error Handling**: Centralized error handling middleware

### Data Layer
- **Supabase PostgreSQL**: Primary database with Row Level Security
- **Supabase Storage**: File storage for avatars and assets
- **Supabase Realtime**: WebSocket connections for live updates

### State Management
- **Server State**: TanStack Query for API data caching
- **Client State**: Zustand for UI state management
- **Form State**: React Hook Form with Valibot validation

## Security Architecture

### Authentication Flow
1. User logs in via Supabase Auth
2. JWT token stored in httpOnly cookie
3. Server-side token validation on protected routes
4. Row Level Security enforced at database level

### Authorization Layers
- **Route-level**: Protected pages check authentication
- **API-level**: Middleware validates tokens and permissions
- **Database-level**: RLS policies enforce data access rules

## Data Flow

### Read Operations
1. Server Component fetches data directly from Supabase
2. Data is serialized and sent to client
3. Client Components use TanStack Query for subsequent requests
4. Cache invalidation triggers refetch when data changes

### Write Operations
1. Client Component submits data via API route
2. API route validates input and authentication
3. Database operation executed with RLS enforcement
4. Response sent back to client
5. TanStack Query cache updated

## Performance Considerations

### Optimization Strategies
- Server Components minimize client-side JavaScript
- Dynamic imports for code splitting
- Image optimization with Next.js Image
- Database query optimization with proper indexing
- Edge caching for static content

### Caching Strategy
- **Static Assets**: CDN caching with long TTL
- **API Responses**: TanStack Query with stale-while-revalidate
- **Database Queries**: Connection pooling and query optimization
- **Build Artifacts**: Incremental Static Regeneration (ISR)
```

## 📝 **Code Documentation Standards**

### Function Documentation
```typescript
/**
 * Creates a new user account with validation and security checks.
 * 
 * This function validates the input data, checks for existing users,
 * creates the user in the database, and sends a welcome email.
 * 
 * @param userData - The user data to create the account
 * @param userData.name - Full name of the user (1-100 characters)
 * @param userData.email - Valid email address (must be unique)
 * @param userData.role - User role ('user' | 'admin')
 * @param userData.avatar - Optional avatar URL
 * 
 * @returns Promise that resolves to the created user object
 * 
 * @throws {ValidationError} When input data is invalid
 * @throws {ConflictError} When email already exists
 * @throws {DatabaseError} When database operation fails
 * 
 * @example
 * ```typescript
 * const user = await createUser({
 *   name: 'John Doe',
 *   email: '<EMAIL>',
 *   role: 'user'
 * })
 * console.log(user.id) // Generated user ID
 * ```
 * 
 * @see {@link getUserById} for retrieving users
 * @see {@link updateUser} for updating user data
 */
export async function createUser(userData: CreateUserInput): Promise<User> {
  // Implementation...
}
```

### Component Documentation
```typescript
/**
 * UserProfile displays user information with edit capabilities.
 * 
 * This component shows user details including name, email, avatar,
 * and role. Admin users can edit any profile, while regular users
 * can only edit their own profile.
 * 
 * @param props - Component props
 * @param props.user - User object to display
 * @param props.currentUser - Currently authenticated user
 * @param props.onEdit - Callback when edit button is clicked
 * @param props.isLoading - Whether the component is in loading state
 * 
 * @example
 * ```tsx
 * <UserProfile
 *   user={selectedUser}
 *   currentUser={authUser}
 *   onEdit={(userId) => navigate(`/users/${userId}/edit`)}
 *   isLoading={false}
 * />
 * ```
 */
interface UserProfileProps {
  user: User
  currentUser: User
  onEdit: (userId: string) => void
  isLoading?: boolean
}

export function UserProfile({ 
  user, 
  currentUser, 
  onEdit, 
  isLoading = false 
}: UserProfileProps) {
  // Implementation...
}
```

### Complex Logic Documentation
```typescript
export function calculateUserPermissions(user: User, resource: Resource): Permissions {
  // Check if user is admin - admins have full access to all resources
  if (user.role === 'admin') {
    return {
      read: true,
      write: true,
      delete: true,
      share: true
    }
  }
  
  // Check if user owns the resource - owners have full access except admin actions
  if (resource.ownerId === user.id) {
    return {
      read: true,
      write: true,
      delete: true,
      share: true
    }
  }
  
  // Check if resource is shared with user - shared users have limited access
  const sharedPermission = resource.sharedWith.find(s => s.userId === user.id)
  if (sharedPermission) {
    return {
      read: true,
      write: sharedPermission.canEdit,
      delete: false,
      share: false
    }
  }
  
  // Check if resource is public - public resources are read-only
  if (resource.isPublic) {
    return {
      read: true,
      write: false,
      delete: false,
      share: false
    }
  }
  
  // Default: no access
  return {
    read: false,
    write: false,
    delete: false,
    share: false
  }
}
```

## 📋 **Documentation Maintenance**

### Review Process
```yaml
documentation_review:
  frequency: "Every sprint/release"
  reviewers: "Technical writers + subject matter experts"
  criteria:
    - Accuracy of technical information
    - Clarity and readability
    - Completeness of examples
    - Up-to-date with latest changes
  
  automated_checks:
    - Link validation
    - Code example compilation
    - Spelling and grammar
    - Markdown formatting
```

### Documentation Automation
```yaml
automation_tools:
  api_docs: "Generate from OpenAPI specs"
  code_docs: "Extract JSDoc comments"
  readme_updates: "Sync with package.json changes"
  changelog: "Generate from git commits"
  
  ci_cd_integration:
    - Documentation builds on every PR
    - Dead link detection
    - Style guide enforcement
    - Automated deployment to docs site
```

This documentation standards file provides the Documenter agent with comprehensive guidelines for creating and maintaining high-quality technical documentation.
