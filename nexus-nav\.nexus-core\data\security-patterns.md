# NEXUS Security Patterns

## 🛡️ **Security-First Development Patterns**

### Authentication & Authorization Patterns
```yaml
auth_patterns:
  jwt_validation:
    server_side_only: true
    token_expiry: 15_minutes
    refresh_strategy: rotation
    storage: httpOnly_cookies
  
  rls_policies:
    mandatory_for_tables: true
    user_isolation: "user_id = auth.uid()"
    role_based_access: true
    resource_level_control: true
  
  oauth_security:
    state_validation: required
    pkce_flow: enforced
    redirect_uri_whitelist: strict
    scope_minimal: principle
```

### Input Validation & Sanitization
```yaml
input_security:
  validation_library: valibot
  server_client_sync: true
  sanitization_points:
    - client_input
    - api_boundaries
    - database_writes
    - file_uploads
  
  common_attacks:
    sql_injection: "Use parameterized queries, ORM protection"
    xss_prevention: "Sanitize HTML, escape output, CSP headers"
    csrf_protection: "CSRF tokens, SameSite cookies"
    path_traversal: "Validate file paths, restrict access"
```

### Database Security Patterns
```yaml
database_security:
  connection_security:
    ssl_required: true
    connection_pooling: secure
    credential_rotation: automated
    environment_isolation: strict
  
  query_security:
    parameterized_queries: mandatory
    stored_procedures: preferred
    dynamic_sql: forbidden
    query_logging: enabled
  
  data_protection:
    encryption_at_rest: true
    encryption_in_transit: true
    pii_handling: special_procedures
    audit_logging: comprehensive
```

### API Security Standards
```yaml
api_security:
  endpoint_protection:
    rate_limiting: per_user_and_global
    input_validation: comprehensive
    output_sanitization: standard
    error_handling: no_info_leaks
  
  authentication_flow:
    bearer_tokens: jwt_based
    token_validation: server_side
    session_management: secure
    logout_cleanup: complete
  
  cors_configuration:
    origins: whitelist_only
    credentials: true
    methods: explicit_list
    headers: minimal_required
```

### Cryptographic Standards
```yaml
crypto_standards:
  hashing:
    passwords: bcrypt_12_rounds
    api_keys: sha256_salted
    sessions: crypto_random
    tokens: jwt_hs256_or_rs256
  
  encryption:
    symmetric: aes_256_gcm
    asymmetric: rsa_2048_or_ecc
    key_management: vault_based
    rotation_schedule: quarterly
  
  random_generation:
    session_ids: crypto_secure
    csrf_tokens: crypto_secure
    api_keys: crypto_secure
    salts: unique_per_hash
```

## 🚨 **Vulnerability Prevention Checklist**

### OWASP Top 10 Prevention
```yaml
owasp_prevention:
  injection_attacks:
    - Use parameterized queries exclusively
    - Validate all input with strict schemas
    - Escape output in all contexts
    - Use ORM/query builders properly
  
  broken_authentication:
    - Implement MFA where possible
    - Use secure session management
    - Enforce strong password policies
    - Monitor for credential stuffing
  
  sensitive_data_exposure:
    - Encrypt data at rest and in transit
    - Minimize data collection
    - Secure data transmission
    - Implement proper access controls
  
  xml_external_entities:
    - Disable XML external entity processing
    - Use JSON instead of XML when possible
    - Validate XML inputs strictly
    - Use secure XML parsers
  
  broken_access_control:
    - Implement deny by default
    - Use role-based access control
    - Verify permissions server-side
    - Log access attempts
```

### Security Headers Configuration
```yaml
security_headers:
  required_headers:
    content_security_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'"
    x_frame_options: "DENY"
    x_content_type_options: "nosniff"
    referrer_policy: "strict-origin-when-cross-origin"
    permissions_policy: "geolocation=(), microphone=(), camera=()"
  
  hsts_configuration:
    max_age: 31536000
    include_subdomains: true
    preload: true
  
  cors_security:
    access_control_allow_origin: "specific_domains_only"
    access_control_allow_credentials: true
    access_control_max_age: 3600
```

## 🔒 **Secure Coding Patterns**

### Error Handling Security
```typescript
// NEXUS Secure Error Pattern
export function secureErrorHandler(error: unknown, context: string) {
  // Log detailed error internally
  console.error(`[${context}] Detailed error:`, error)
  
  // Return sanitized error to client
  if (error instanceof ValidationError) {
    return { error: 'Invalid input provided' }
  }
  
  if (error instanceof AuthenticationError) {
    return { error: 'Authentication required' }
  }
  
  if (error instanceof AuthorizationError) {
    return { error: 'Access denied' }
  }
  
  // Never expose internal errors
  return { error: 'An unexpected error occurred' }
}
```

### Secure Data Access Pattern
```typescript
// NEXUS Secure Data Access
export async function secureDataAccess<T>(
  operation: () => Promise<T>,
  userId: string,
  resource: string
): Promise<T> {
  // Verify authentication
  if (!userId) {
    throw new AuthenticationError('User not authenticated')
  }
  
  // Check authorization
  const hasAccess = await verifyAccess(userId, resource)
  if (!hasAccess) {
    throw new AuthorizationError('Access denied to resource')
  }
  
  // Log access attempt
  await logAccess(userId, resource, 'READ')
  
  // Execute operation with RLS protection
  return await operation()
}
```

### Input Validation Pattern
```typescript
// NEXUS Secure Input Validation
import { object, string, parse } from 'valibot'

const UserInputSchema = object({
  email: string([email(), maxLength(255)]),
  name: string([minLength(1), maxLength(100), regex(/^[a-zA-Z\s]+$/)]),
  role: string([includes(['user', 'admin', 'viewer'])])
})

export function validateUserInput(input: unknown) {
  try {
    return parse(UserInputSchema, input)
  } catch (error) {
    throw new ValidationError('Invalid user input', error)
  }
}
```

## 🎯 **Security Monitoring Patterns**

### Audit Logging
```yaml
audit_requirements:
  authentication_events:
    - login_attempts
    - logout_events
    - password_changes
    - failed_authentications
  
  authorization_events:
    - access_granted
    - access_denied
    - permission_changes
    - role_modifications
  
  data_access_events:
    - data_reads
    - data_writes
    - data_deletes
    - export_operations
```

This security patterns file provides the Analyzer agent with comprehensive security knowledge for identifying vulnerabilities and enforcing secure coding practices.
