title: "Supabase Architecture Document"
description: "Template for Supabase backend architecture with PostgreSQL, Auth, Storage, and Realtime"
version: "1.0.0"
framework: "NEXUS"

sections:
  - id: overview
    title: "Supabase Architecture Overview"
    instruction: "Provide high-level Supabase backend architecture"
    template: |
      # {{project_name}} Supabase Architecture
      
      ## Backend Overview
      {{backend_overview}}
      
      ## Supabase Services
      - **Database**: PostgreSQL with Row Level Security (RLS)
      - **Authentication**: Multi-provider auth with JWT
      - **Storage**: File storage with CDN
      - **Realtime**: WebSocket subscriptions
      - **Edge Functions**: Serverless functions (if needed)
      
      ## Scale Requirements
      {{scale_requirements}}

  - id: database_architecture
    title: "Database Architecture"
    instruction: "Define PostgreSQL database structure and optimization"
    template: |
      ## Database Architecture
      
      ### Schema Design
      {{schema_design}}
      
      ### Primary Key Strategy
      - **Type**: XID (Extended ID) for global uniqueness
      - **Format**: 20-character alphanumeric string
      - **Benefits**: URL-safe, sortable, collision-resistant
      
      ### Indexing Strategy
      ```sql
      {{indexing_strategy}}
      ```
      
      ### Performance Optimization
      {{performance_optimization}}

  - id: rls_security
    title: "Row Level Security (RLS)"
    instruction: "Define comprehensive RLS policies for multi-tenant security"
    template: |
      ## Row Level Security Implementation
      
      ### Security Strategy
      {{security_strategy}}
      
      ### RLS Policies
      ```sql
      -- Enable RLS on tables
      ALTER TABLE {{table_name}} ENABLE ROW LEVEL SECURITY;
      
      -- Create policies
      {{rls_policies}}
      ```
      
      ### User Roles and Permissions
      {{user_roles_permissions}}
      
      ### Policy Testing
      {{policy_testing}}

  - id: authentication
    title: "Authentication Architecture"
    instruction: "Define Supabase Auth implementation and user management"
    template: |
      ## Authentication Architecture
      
      ### Auth Providers
      {{auth_providers}}
      
      ### User Management
      ```typescript
      // Client-side auth
      import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
      
      const supabase = createClientComponentClient();
      
      // Sign up
      const { data, error } = await supabase.auth.signUp({
        email: '{{user_email}}',
        password: '{{user_password}}',
      });
      ```
      
      ### Session Management
      {{session_management}}
      
      ### User Profiles
      {{user_profiles}}

  - id: storage_architecture
    title: "Storage Architecture"
    instruction: "Define file storage strategy and CDN optimization"
    template: |
      ## Storage Architecture
      
      ### Bucket Organization
      ```
      Storage Buckets:
      ├── avatars/              # User profile images
      ├── documents/            # User documents
      ├── public/               # Public assets
      └── private/              # Private user files
      ```
      
      ### Storage Policies
      ```sql
      {{storage_policies}}
      ```
      
      ### File Upload Patterns
      ```typescript
      // File upload implementation
      const uploadFile = async (file: File, bucket: string, path: string) => {
        {{upload_implementation}}
      };
      ```
      
      ### CDN and Optimization
      {{cdn_optimization}}

  - id: realtime_architecture
    title: "Realtime Architecture"
    instruction: "Define real-time subscriptions and WebSocket usage"
    template: |
      ## Realtime Architecture
      
      ### Subscription Strategy
      {{subscription_strategy}}
      
      ### Real-time Channels
      ```typescript
      // Real-time subscription
      const channel = supabase
        .channel('{{channel_name}}')
        .on('postgres_changes', {
          event: '{{event_type}}',
          schema: 'public',
          table: '{{table_name}}',
        }, (payload) => {
          {{subscription_handler}}
        })
        .subscribe();
      ```
      
      ### Performance Considerations
      {{realtime_performance}}
      
      ### Connection Management
      {{connection_management}}

  - id: api_integration
    title: "API Integration"
    instruction: "Define how Next.js integrates with Supabase APIs"
    template: |
      ## API Integration
      
      ### Client Configuration
      ```typescript
      // lib/supabase/client.ts
      import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
      import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
      import { cookies } from 'next/headers';
      
      // Client-side
      export const createClient = () => createClientComponentClient();
      
      // Server-side
      export const createServerClient = () => 
        createServerComponentClient({ cookies });
      ```
      
      ### Data Fetching Patterns
      {{data_fetching_patterns}}
      
      ### Error Handling
      {{error_handling}}

  - id: security_implementation
    title: "Security Implementation"
    instruction: "Define comprehensive security measures"
    template: |
      ## Security Implementation
      
      ### API Security
      {{api_security}}
      
      ### Data Validation
      ```typescript
      // Valibot validation schemas
      import { object, string, email } from 'valibot';
      
      const {{schema_name}} = object({
        {{validation_schema}}
      });
      ```
      
      ### Rate Limiting
      {{rate_limiting}}
      
      ### Audit Logging
      {{audit_logging}}

  - id: performance_monitoring
    title: "Performance Monitoring"
    instruction: "Define monitoring and optimization strategies"
    template: |
      ## Performance Monitoring
      
      ### Database Performance
      {{database_performance}}
      
      ### Query Optimization
      {{query_optimization}}
      
      ### Connection Pooling
      {{connection_pooling}}
      
      ### Monitoring Tools
      {{monitoring_tools}}

  - id: backup_recovery
    title: "Backup and Recovery"
    instruction: "Define backup and disaster recovery procedures"
    template: |
      ## Backup and Recovery
      
      ### Backup Strategy
      {{backup_strategy}}
      
      ### Point-in-Time Recovery
      {{point_in_time_recovery}}
      
      ### Data Export/Import
      {{data_export_import}}
      
      ### Disaster Recovery Plan
      {{disaster_recovery_plan}}

placeholders:
  project_name: "Project name"
  backend_overview: "High-level backend description and architecture"
  scale_requirements: "Expected scale and performance requirements"
  schema_design: "Database schema design overview"
  indexing_strategy: "Database indexing for performance"
  performance_optimization: "Database performance optimization techniques"
  security_strategy: "Overall security approach for multi-tenancy"
  table_name: "Database table name"
  rls_policies: "Row Level Security policy definitions"
  user_roles_permissions: "User roles and database permissions"
  policy_testing: "How to test RLS policies"
  auth_providers: "Authentication providers (email, OAuth, etc.)"
  user_email: "User email for auth examples"
  user_password: "User password for auth examples"
  session_management: "Session management and refresh strategy"
  user_profiles: "User profile management implementation"
  storage_policies: "Storage bucket security policies"
  upload_implementation: "File upload implementation code"
  cdn_optimization: "CDN and file optimization strategies"
  subscription_strategy: "Real-time subscription organization"
  channel_name: "Real-time channel name"
  event_type: "Real-time event type (INSERT, UPDATE, DELETE)"
  subscription_handler: "Real-time event handler implementation"
  realtime_performance: "Real-time performance considerations"
  connection_management: "WebSocket connection management"
  data_fetching_patterns: "Data fetching patterns and best practices"
  error_handling: "Error handling and retry strategies"
  api_security: "API security implementation"
  schema_name: "Validation schema name"
  validation_schema: "Valibot validation schema definition"
  rate_limiting: "Rate limiting implementation"
  audit_logging: "Audit logging and compliance"
  database_performance: "Database performance monitoring"
  query_optimization: "Query optimization techniques"
  connection_pooling: "Connection pooling configuration"
  monitoring_tools: "Monitoring and alerting tools"
  backup_strategy: "Automated backup procedures"
  point_in_time_recovery: "Point-in-time recovery procedures"
  data_export_import: "Data export and import procedures"
  disaster_recovery_plan: "Disaster recovery planning"

validation:
  required_sections: ["overview", "database_architecture", "rls_security", "authentication", "security_implementation"]
  rls_required: true
  xid_primary_keys: true
  auth_required: true
  security_review: true
