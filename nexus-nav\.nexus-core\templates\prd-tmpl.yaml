template:
  id: prd-template-nexus-v1
  name: Product Requirements Document - NEXUS Framework
  version: 1.0
  output:
    format: markdown
    filename: docs/prd.md
    title: "{{project_name}} Product Requirements Document (PRD)"

workflow:
  mode: interactive
  elicitation: advanced-elicitation

sections:
  - id: goals-context
    title: Goals and Background Context
    instruction: |
      Gather the foundational information for this PRD. Ask if a Project Brief exists - if not, recommend creating one first as it provides essential foundation. If Project Brief exists, use it to populate Goals (bullet list of desired outcomes) and Background Context (1-2 paragraphs on problem being solved). Include a Change Log table to track document versions.
    sections:
      - id: goals
        title: Goals
        type: bullet-list
        instruction: Bullet list of 1-line desired outcomes the PRD will deliver if successful
      - id: background
        title: Background Context
        type: paragraphs
        instruction: 1-2 short paragraphs summarizing what problem this solves and why it matters
      - id: changelog
        title: Change Log
        type: table
        columns: [Date, Version, Description, Author]
        instruction: Track document versions and changes

  - id: requirements
    title: Requirements
    instruction: Draft functional and non-functional requirements for the NEXUS tech stack
    elicit: true
    sections:
      - id: functional
        title: Functional Requirements
        type: numbered-list
        prefix: FR
        instruction: |
          Each requirement should be testable and specific to the feature set. Consider Next.js App Router patterns, React Server Components, and Supabase integration points.
        examples:
          - "FR1: Users can authenticate using Supabase Auth with email/password and OAuth providers"
          - "FR2: The dashboard displays real-time data using TanStack Query with automatic background refetching"
          - "FR3: All forms use Valibot for client and server-side validation with TypeScript integration"
      - id: non-functional
        title: Non-Functional Requirements
        type: numbered-list
        prefix: NFR
        instruction: |
          Focus on performance, security, accessibility, and scalability requirements specific to the NEXUS tech stack.
        examples:
          - "NFR1: Page load times must be under 2 seconds with Next.js optimization"
          - "NFR2: All components must pass WCAG AA accessibility standards"
          - "NFR3: Supabase Row Level Security must protect all user data access"

  - id: tech-architecture
    title: Technical Architecture
    condition: PRD involves technical implementation
    instruction: |
      Define the technical approach using the NEXUS framework stack. This guides the Architect agent's work.
    elicit: true
    sections:
      - id: tech-stack
        title: Technology Stack
        instruction: |
          Confirm the NEXUS framework components being used:
          - Next.js 15.4+ (App Router)
          - React 19 (Server Components)
          - TypeScript 5.8+
          - Supabase (Database + Auth + Storage)
          - Zustand 5+ (Client State)
          - TanStack Query v5 (Server State)
          - Valibot v1.1.0 (Validation)
          - Tailwind CSS 4.0+
          - Shadcn/ui Components
      - id: data-architecture
        title: Data Architecture
        instruction: High-level database design considerations for Supabase PostgreSQL
      - id: api-design
        title: API Design
        instruction: API structure using Next.js App Router API routes and Supabase integration
      - id: state-management
        title: State Management
        instruction: How client state (Zustand) and server state (TanStack Query) will be organized

  - id: ui-ux-requirements
    title: User Interface & Experience Requirements
    condition: PRD has UI/UX components
    instruction: |
      Capture high-level UI/UX vision using Tailwind CSS and Shadcn/ui components.
    elicit: true
    choices:
      accessibility: [WCAG AA, WCAG AAA, Custom Requirements]
      responsive: [Mobile First, Desktop First, Progressive Enhancement]
    sections:
      - id: design-system
        title: Design System
        instruction: How Tailwind CSS and Shadcn/ui will be customized for this project
      - id: responsive-design
        title: Responsive Design
        instruction: Breakpoint strategy and mobile-first considerations
      - id: accessibility
        title: Accessibility Requirements
        instruction: WCAG compliance level and specific accessibility features needed
      - id: key-screens
        title: Key Screens and User Flows
        instruction: |
          From a product perspective, what are the critical screens or views needed to deliver the PRD goals?
        examples:
          - "Authentication flow (login/register/forgot password)"
          - "Main dashboard with real-time data"
          - "User profile and settings"
          - "Data entry forms with validation"

  - id: security-requirements
    title: Security Requirements
    instruction: |
      Define security requirements leveraging Supabase security features and NEXUS framework patterns.
    elicit: true
    sections:
      - id: authentication
        title: Authentication & Authorization
        instruction: Supabase Auth configuration and role-based access control
      - id: data-protection
        title: Data Protection
        instruction: Row Level Security policies and data encryption requirements
      - id: input-validation
        title: Input Validation
        instruction: Valibot schema validation and sanitization requirements
      - id: security-headers
        title: Security Headers
        instruction: Next.js security headers and CSP configuration

  - id: performance-requirements
    title: Performance Requirements
    instruction: |
      Define performance standards achievable with the NEXUS tech stack optimization.
    elicit: true
    sections:
      - id: loading-performance
        title: Loading Performance
        instruction: Core Web Vitals targets and Next.js optimization strategies
      - id: runtime-performance
        title: Runtime Performance
        instruction: React performance considerations and state management efficiency
      - id: database-performance
        title: Database Performance
        instruction: Supabase query optimization and caching strategies
      - id: bundle-optimization
        title: Bundle Optimization
        instruction: Code splitting and tree shaking requirements with Next.js

  - id: testing-strategy
    title: Testing Strategy
    instruction: |
      Define testing approach for the NEXUS framework components.
    sections:
      - id: unit-testing
        title: Unit Testing
        instruction: Component and utility function testing strategy
      - id: integration-testing
        title: Integration Testing
        instruction: API route and database integration testing
      - id: e2e-testing
        title: End-to-End Testing
        instruction: Critical user journey testing approach
      - id: performance-testing
        title: Performance Testing
        instruction: Load testing and performance monitoring strategy

  - id: deployment-requirements
    title: Deployment & Operations
    instruction: |
      Define deployment strategy using Vercel or similar platforms optimized for Next.js.
    sections:
      - id: hosting-strategy
        title: Hosting Strategy
        instruction: Vercel deployment configuration and environment management
      - id: database-deployment
        title: Database Deployment
        instruction: Supabase project setup and migration strategy
      - id: monitoring
        title: Monitoring & Observability
        instruction: Error tracking, performance monitoring, and logging requirements
      - id: maintenance
        title: Maintenance & Updates
        instruction: Dependency updates and framework version management

metadata:
  framework: NEXUS
  tech_stack_version: "1.0"
  created_by: "NEXUS Framework Generator"
  last_updated: "{{current_date}}"
