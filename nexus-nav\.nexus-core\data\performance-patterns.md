# NEXUS Performance Patterns

## ⚡ **Performance-First Development Patterns**

### React Server Components Optimization
```yaml
rsc_patterns:
  data_fetching:
    - "Fetch data in Server Components, not useEffect"
    - "Use async/await for database queries"
    - "Parallel data fetching with Promise.all"
    - "Cache expensive operations with React.cache"
  
  component_splitting:
    - "Server Components for data, Client for interactivity"
    - "Minimize Client Component boundaries"
    - "Use Suspense for loading states"
    - "Error boundaries for failure handling"
  
  streaming_optimization:
    - "Stream UI with loading.tsx"
    - "Progressive enhancement patterns"
    - "Selective hydration strategies"
    - "Priority-based rendering"
```

### Next.js 15+ Performance Patterns
```yaml
nextjs_optimization:
  app_router_performance:
    - "Static generation for cacheable content"
    - "Dynamic imports for code splitting"
    - "Route-level caching strategies"
    - "Metadata optimization for SEO"
  
  image_optimization:
    - "Next.js Image component exclusively"
    - "WebP/AVIF format preference"
    - "Responsive image sizing"
    - "Lazy loading by default"
  
  bundle_optimization:
    - "Tree shaking for unused code"
    - "Dynamic imports for heavy libraries"
    - "Webpack bundle analyzer usage"
    - "Module federation for micro-frontends"
```

### Database Performance Patterns
```yaml
database_optimization:
  query_performance:
    - "Proper indexing strategies"
    - "Query optimization techniques"
    - "Connection pooling configuration"
    - "Prepared statement usage"
  
  supabase_patterns:
    - "RLS policy optimization"
    - "Efficient joins and relationships"
    - "Real-time subscription management"
    - "Edge function utilization"
  
  caching_strategies:
    - "Query result caching"
    - "Redis for session storage"
    - "CDN for static assets"
    - "Browser cache optimization"
```

### State Management Performance
```yaml
state_optimization:
  zustand_patterns:
    - "Atomic state slices"
    - "Computed values with selectors"
    - "Middleware for persistence"
    - "DevTools integration"
  
  tanstack_query:
    - "Query deduplication"
    - "Background refetching"
    - "Optimistic updates"
    - "Infinite query patterns"
  
  memory_management:
    - "Cleanup subscriptions"
    - "Weak references for large objects"
    - "Memory leak detection"
    - "Garbage collection optimization"
```

## 🎯 **Performance Measurement Patterns**

### Core Web Vitals Optimization
```yaml
web_vitals:
  largest_contentful_paint:
    target: "<2.5s"
    techniques:
      - "Optimize images and fonts"
      - "Remove unused CSS/JS"
      - "Use CDN for static assets"
      - "Preload critical resources"
  
  first_input_delay:
    target: "<100ms"
    techniques:
      - "Code splitting for main thread"
      - "Web Workers for heavy tasks"
      - "Debounce user interactions"
      - "Minimize JavaScript execution"
  
  cumulative_layout_shift:
    target: "<0.1"
    techniques:
      - "Set image dimensions"
      - "Reserve space for dynamic content"
      - "Use transform for animations"
      - "Avoid layout-triggering properties"
```

### Performance Monitoring
```yaml
monitoring_patterns:
  real_user_monitoring:
    - "Track Core Web Vitals"
    - "Monitor API response times"
    - "Measure page load speeds"
    - "Track user interaction delays"
  
  synthetic_monitoring:
    - "Lighthouse CI integration"
    - "Performance regression testing"
    - "Cross-browser performance testing"
    - "Mobile performance validation"
  
  performance_budgets:
    bundle_size: "<500KB"
    initial_load: "<3s"
    time_to_interactive: "<5s"
    api_response: "<200ms"
```

## 🚀 **Optimization Techniques**

### Code-Level Optimizations
```typescript
// NEXUS Performance Pattern: Memoization
import { memo, useMemo, useCallback } from 'react'

// Expensive Component Memoization
export const ExpensiveComponent = memo(function ExpensiveComponent({ 
  data, 
  onUpdate 
}: Props) {
  // Memoize expensive calculations
  const processedData = useMemo(() => {
    return expensiveDataProcessing(data)
  }, [data])
  
  // Memoize callback functions
  const handleUpdate = useCallback((newData) => {
    onUpdate(newData)
  }, [onUpdate])
  
  return <div>{processedData}</div>
})

// React.cache for Server Components
import { cache } from 'react'

export const getCachedData = cache(async (id: string) => {
  return await expensiveDataFetch(id)
})
```

### Database Query Optimization
```typescript
// NEXUS Performance Pattern: Efficient Queries
export async function getOptimizedUserData(userId: string) {
  const supabase = createClient()
  
  // Single query with joins instead of multiple queries
  const { data, error } = await supabase
    .from('users')
    .select(`
      id,
      name,
      email,
      profiles (
        avatar_url,
        bio
      ),
      posts (
        id,
        title,
        created_at
      )
    `)
    .eq('id', userId)
    .order('created_at', { foreignTable: 'posts', ascending: false })
    .limit(10, { foreignTable: 'posts' })
    .single()
  
  return data
}
```

### Bundle Size Optimization
```typescript
// NEXUS Performance Pattern: Dynamic Imports
import { lazy, Suspense } from 'react'

// Lazy load heavy components
const HeavyChart = lazy(() => import('./HeavyChart'))
const RichTextEditor = lazy(() => import('./RichTextEditor'))

export function Dashboard() {
  return (
    <div>
      <Suspense fallback={<ChartSkeleton />}>
        <HeavyChart data={chartData} />
      </Suspense>
      
      <Suspense fallback={<EditorSkeleton />}>
        <RichTextEditor content={content} />
      </Suspense>
    </div>
  )
}

// Dynamic library imports
export async function processLargeFile(file: File) {
  // Only load when needed
  const { processFile } = await import('./heavyFileProcessor')
  return processFile(file)
}
```

## 📊 **Performance Anti-Patterns to Avoid**

### React Performance Anti-Patterns
```yaml
react_antipatterns:
  avoid_these:
    - "Creating objects in render functions"
    - "Using array index as key"
    - "Excessive useEffect dependencies"
    - "Not memoizing expensive calculations"
    - "Prop drilling instead of context"
    - "Large component render trees"
    - "Synchronous expensive operations"
    - "Memory leaks from uncleaned subscriptions"
```

### Database Anti-Patterns
```yaml
database_antipatterns:
  avoid_these:
    - "N+1 query problems"
    - "Missing database indexes"
    - "Fetching unnecessary columns"
    - "Client-side data processing"
    - "Synchronous database calls"
    - "Large result set pagination"
    - "Inefficient RLS policies"
    - "Connection pool exhaustion"
```

### Network Anti-Patterns
```yaml
network_antipatterns:
  avoid_these:
    - "Blocking network requests"
    - "Unnecessary API calls"
    - "Large payload sizes"
    - "Missing request deduplication"
    - "No caching strategies"
    - "Synchronous data fetching"
    - "Missing compression"
    - "Inefficient polling"
```

## 🛠️ **Performance Tools & Techniques**

### Development Tools
```yaml
performance_tools:
  browser_tools:
    - "Chrome DevTools Performance tab"
    - "Lighthouse audits"
    - "WebPageTest analysis"
    - "Core Web Vitals extension"
  
  build_analysis:
    - "Webpack Bundle Analyzer"
    - "Next.js Bundle Analyzer"
    - "Source map explorer"
    - "Bundle size tracking"
  
  monitoring_services:
    - "Vercel Analytics"
    - "Google PageSpeed Insights"
    - "GTmetrix monitoring"
    - "Pingdom website speed test"
```

### Automated Performance Testing
```yaml
automation_patterns:
  ci_cd_integration:
    - "Lighthouse CI in build pipeline"
    - "Performance regression tests"
    - "Bundle size limit enforcement"
    - "Core Web Vitals monitoring"
  
  performance_gates:
    - "Fail builds on performance regression"
    - "Bundle size increase alerts"
    - "Lighthouse score thresholds"
    - "API response time limits"
```

This performance patterns file provides both Analyzer and Optimizer agents with comprehensive performance optimization knowledge for identifying bottlenecks and implementing efficient solutions.
