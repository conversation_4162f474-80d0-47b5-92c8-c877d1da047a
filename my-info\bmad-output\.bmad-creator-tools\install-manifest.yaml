version: 1.8.0
installed_at: '2025-07-14T00:47:56.998Z'
install_type: expansion-pack
expansion_pack_id: bmad-creator-tools
expansion_pack_name: bmad-creator-tools
ides_setup:
  - cursor
  - trae
  - github-copilot
files:
  - path: .bmad-creator-tools\README.md
    hash: d51c7e2cda6cc300
    modified: false
  - path: .bmad-creator-tools\config.yaml
    hash: 8230b3f3e2fe47bd
    modified: false
  - path: .bmad-creator-tools\utils\workflow-management.md
    hash: b148df3ebb1f9c61
    modified: false
  - path: .bmad-creator-tools\utils\bmad-doc-template.md
    hash: 4b2f7c4408835b9e
    modified: false
  - path: .bmad-creator-tools\templates\expansion-pack-plan-tmpl.yaml
    hash: c5b67a7229db2e46
    modified: false
  - path: .bmad-creator-tools\templates\agent-tmpl.yaml
    hash: 441befc60abf9057
    modified: false
  - path: .bmad-creator-tools\templates\agent-teams-tmpl.yaml
    hash: 5f7426c4edd2f7c6
    modified: false
  - path: .bmad-creator-tools\tasks\generate-expansion-pack.md
    hash: a5d0a9fc6211bae6
    modified: false
  - path: .bmad-creator-tools\tasks\execute-checklist.md
    hash: 342da4e4404f3fa6
    modified: false
  - path: .bmad-creator-tools\tasks\create-doc.md
    hash: 1afe65198a4cdcc2
    modified: false
  - path: .bmad-creator-tools\tasks\create-deep-research-prompt.md
    hash: 5716b19ae78b3afb
    modified: false
  - path: .bmad-creator-tools\tasks\create-agent.md
    hash: 73aa0df4c7d6484d
    modified: false
  - path: .bmad-creator-tools\tasks\advanced-elicitation.md
    hash: 28e3b538dc6fe104
    modified: false
  - path: .bmad-creator-tools\agents\bmad-the-creator.md
    hash: 72d550d93328b7fb
    modified: false
