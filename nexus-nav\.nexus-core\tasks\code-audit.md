# Code Quality Assessment Task

## Overview
Perform comprehensive code analysis covering quality, security, performance, and maintainability aspects.

## Analysis Areas

### 1. Code Quality
- **Readability**: Clear variable names, function structure, comments
- **Maintainability**: Code organization, modularity, reusability
- **Consistency**: Coding style, pattern adherence, convention following
- **Complexity**: Cyclomatic complexity, nesting levels, function length

### 2. Security Analysis
- **Input Validation**: Check for proper validation using Valibot
- **Authentication**: Verify Supabase Auth implementation
- **Authorization**: Ensure Row Level Security (RLS) policies
- **Data Sanitization**: Check for XSS, SQL injection protection
- **Secrets Management**: Environment variables, API key handling

### 3. Performance Assessment
- **Bundle Analysis**: Import statements, tree shaking opportunities
- **React Performance**: useMemo, useCallback, React.memo usage
- **Database Queries**: Supabase query optimization
- **Image Optimization**: Next.js Image component usage
- **Caching**: TanStack Query cache configuration

### 4. TypeScript Quality
- **Type Safety**: Strict mode compliance, any usage
- **Type Definitions**: Interface completeness, type exports
- **Generic Usage**: Proper generic constraints and defaults
- **Error Handling**: Typed error boundaries and try-catch

### 5. Next.js Best Practices
- **App Router**: Proper page/layout structure
- **Server Components**: Appropriate RSC usage
- **API Routes**: Route handler implementation
- **Metadata**: SEO and meta tag implementation
- **Performance**: Loading states, error boundaries

### 6. Supabase Integration
- **Client Configuration**: Proper client initialization
- **RLS Policies**: Row Level Security implementation
- **Real-time**: Subscription handling and cleanup
- **Edge Functions**: Proper function structure
- **Storage**: File upload/download security

## Analysis Process

### Step 1: Initial Assessment
1. Review overall project structure
2. Check package.json for dependencies
3. Examine tsconfig.json configuration
4. Review Next.js configuration files

### Step 2: Code Review
1. Analyze component structure and patterns
2. Review API route implementations
3. Check database schema and queries
4. Examine state management patterns
5. Review utility functions and helpers

### Step 3: Security Scan
1. Check for hardcoded secrets
2. Verify input validation patterns
3. Review authentication flows
4. Examine authorization checks
5. Check for potential vulnerabilities

### Step 4: Performance Analysis
1. Analyze bundle composition
2. Check for performance anti-patterns
3. Review query optimization
4. Examine caching strategies
5. Check image and asset optimization

## Report Generation

### Findings Structure
```markdown
# Code Audit Report

## Executive Summary
- Overall quality score (1-10)
- Critical issues count
- Key recommendations

## Detailed Findings

### 🔴 Critical Issues
- Security vulnerabilities
- Performance blockers
- Type safety violations

### 🟡 Warnings
- Code smells
- Performance improvements
- Best practice violations

### 🟢 Good Practices
- Well-implemented patterns
- Security measures
- Performance optimizations

## Recommendations
1. Immediate actions required
2. Short-term improvements
3. Long-term architecture changes

## Implementation Guide
- Step-by-step fixes
- Code examples
- Resource links
```

## Quality Standards

### NEXUS Framework Compliance
- TypeScript strict mode enabled
- Valibot for all input validation
- Supabase RLS for data access
- TanStack Query for server state
- Zustand for client state
- Next.js App Router patterns
- React 19 Server Components

### Security Requirements
- All inputs validated with Valibot schemas
- Supabase RLS policies implemented
- Authentication flows secure
- No hardcoded secrets
- OWASP compliance

### Performance Targets
- Core Web Vitals passing
- Bundle size optimized
- Database queries efficient
- Images optimized
- Proper caching implemented

## Tools Integration
- Use TypeScript compiler for type checking
- Reference ESLint/Prettier configurations
- Check Lighthouse scores if available
- Review bundle analyzer reports
- Examine test coverage reports
