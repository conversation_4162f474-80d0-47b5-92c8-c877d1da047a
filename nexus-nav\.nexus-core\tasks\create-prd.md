# Create Product Requirements Document (YAML Driven)

## Critical: Template Discovery

If a YAML Template has not been provided, list all templates from .nexus-core/templates or ask the user to provide another.

## CRITICAL: Mandatory Elicitation Format

**When `elicit: true`, ALWAYS use this exact format:**

1. Present section content
2. Provide detailed rationale (explain trade-offs, assumptions, decisions made)
3. Present numbered options 1-9:
   - **Option 1:** Always "Proceed to next section"
   - **Options 2-9:** Select 8 methods from data/elicitation-methods
   - End with: "Select 1-9 or just type your question/feedback:"

**NEVER ask yes/no questions or use any other format.**

## Processing Flow

1. **Parse YAML template** - Load template metadata and sections
2. **Set preferences** - Show current mode (Interactive), confirm output file
3. **Process each section:**
   - Skip if condition unmet
   - Check agent permissions (owner/editors) - note if section is restricted to specific agents
   - Draft content using section instruction
   - Present content + detailed rationale
   - **IF elicit: true** → MANDATORY 1-9 options format
   - Save to file if possible
4. **Continue until complete**

## Detailed Rationale Requirements

When presenting section content, ALWAYS include rationale that explains:

- Trade-offs and choices made (what was chosen over alternatives and why)
- Key assumptions made during drafting
- Interesting or questionable decisions that need user attention
- Areas that might need validation

## Elicitation Results Flow

After user selects elicitation method (2-9):

1. Execute method from data/elicitation-methods
2. Present results with insights
3. Offer options:
   - **1. Apply changes and update section**
   - **2. Return to elicitation menu**

## Output Management

- Default output: `docs/prd.md`
- Create directories as needed
- Update existing files (show diff when possible)
- Track completion status

## Section Processing Rules

- Process sections in order defined by template
- Handle nested sections recursively
- Apply conditional logic (skip sections based on conditions)
- Maintain section numbering and hierarchy
- Generate table of contents automatically

## Integration with Tech Stack

When creating PRDs for NEXUS framework projects:

- Assume Next.js 15.4+ with App Router
- Include React 19 Server Component considerations
- Consider Supabase backend requirements
- Plan for TypeScript 5.8+ strict mode
- Include Tailwind CSS 4.0+ for styling
- Consider Zustand for state management
- Plan TanStack Query for server state
- Use Valibot for input validation

## Quality Standards

- All sections must be actionable
- Requirements must be testable
- Include acceptance criteria
- Consider edge cases
- Plan for accessibility
- Include security considerations
- Consider performance implications
