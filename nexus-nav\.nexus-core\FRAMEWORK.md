# NEXUS Framework Documentation

## Overview
NEXUS is a micro-agent system designed for efficient, production-ready development through specialized AI agents that collaborate seamlessly.

## Core Principles

### 1. Context Intelligence
- Each agent maintains awareness of project context
- Agents reference `.nexus-core/data/` files for standards and patterns
- Context continuity preserved across agent interactions
- Smart context injection ensures relevant information is always available

### 2. Production-Ready Standards
- All code must follow standards in `.nexus-core/data/coding-standards.md`
- Security patterns from `.nexus-core/data/security-patterns.md`
- Performance patterns from `.nexus-core/data/performance-patterns.md`
- Component patterns from `.nexus-core/data/component-patterns.md`

### 3. Agent Collaboration
- Agents can hand-off work to other specialized agents
- Use `@{agent-name}` to transfer context to appropriate specialist
- Maintain conversation continuity across agent switches
- Each agent focuses on their core expertise area

## File Structure Awareness

### Core Directory Structure
```
.nexus-core/
├── agents/           # Agent definitions and capabilities
├── data/            # Shared knowledge base and patterns
├── tasks/           # Reusable task definitions
├── templates/       # Code and document templates
└── core-config.yaml # Framework configuration
```

### Agent File Structure Understanding
- Agents understand project structure via `.nexus-core/`
- Reference templates in `.nexus-core/templates/`
- Execute tasks from `.nexus-core/tasks/`
- Follow patterns from `.nexus-core/data/`

### Data Dependencies
All agents reference these core data files:
- `coding-standards.md` - Code quality and style standards
- `security-patterns.md` - Security implementation patterns
- `performance-patterns.md` - Performance optimization patterns
- `component-patterns.md` - UI/UX component patterns
- `api-patterns.md` - API design and implementation patterns
- `testing-standards.md` - Testing strategies and standards
- `tech-stack-preferences.md` - Technology stack guidelines
- `documentation-standards.md` - Documentation requirements

## Usage Guidelines

### 1. Agent Activation Workflow
```
1. Start with Analysis: Use @analyzer for new features or problems
2. Design Before Build: Use @architect for system design
3. Implement with Standards: Use @implementer for development
4. Validate Everything: Use @validator for quality assurance
5. Optimize Continuously: Use @optimizer for performance
6. Document Thoroughly: Use @documenter for documentation
```

### 2. Agent Activation Format
- **Trigger Format**: Use `@{agent-name}` to activate agents
- **Command Format**: Use `*{command}` to execute specific agent commands
- **Help Command**: Always start with `*help` to see available commands

### 3. Available Agents
- **@analyzer** - System Analysis & Requirements
- **@architect** - System Design & Architecture
- **@implementer** - Code Development & Implementation
- **@validator** - Quality Assurance & Testing
- **@optimizer** - Performance & Optimization
- **@documenter** - Documentation & Knowledge Management

### 4. Best Practices
- Always maintain context continuity across agent interactions
- Reference appropriate data files for standards and patterns
- Use templates for consistent code structure
- Follow the established task workflows
- Ensure all work meets quality standards

## Quality Standards

### Code Quality Requirements
- Follow coding standards from `.nexus-core/data/coding-standards.md`
- Implement security patterns from `.nexus-core/data/security-patterns.md`
- Apply performance patterns from `.nexus-core/data/performance-patterns.md`
- Use component patterns from `.nexus-core/data/component-patterns.md`
- Follow API patterns from `.nexus-core/data/api-patterns.md`
- Adhere to testing standards from `.nexus-core/data/testing-standards.md`
- Reference tech stack preferences from `.nexus-core/data/tech-stack-preferences.md`
- Follow documentation standards from `.nexus-core/data/documentation-standards.md`

### Quality Assurance Process
1. **Code Review**: All code must pass validator agent review
2. **Testing**: Comprehensive testing following testing standards
3. **Security**: Security patterns must be implemented
4. **Performance**: Performance patterns must be applied
5. **Documentation**: Documentation standards must be followed

### Continuous Improvement
- Regular optimization through optimizer agent
- Continuous monitoring of quality metrics
- Regular updates to patterns and standards
- Knowledge base maintenance and expansion

## Agent Collaboration Model

### Context Handoff Protocol
1. **Current Agent**: Provides context summary
2. **Target Agent**: Receives context and confirms understanding
3. **Continuation**: Work continues with full context awareness
4. **Documentation**: All handoffs are documented for continuity

### Shared Knowledge Base
- All agents access the same data files for consistency
- Patterns and standards are centrally maintained
- Context engine ensures relevant information is always available
- Knowledge base evolves with project requirements

## Framework Evolution

### Continuous Learning
- Framework patterns evolve based on project success
- New patterns are added to the knowledge base
- Outdated patterns are deprecated and removed
- Best practices are continuously refined

### Scalability
- Framework scales with project complexity
- New agents can be added as needed
- Data files can be expanded for new domains
- Task definitions can be customized for specific needs

---

*This framework documentation serves as the central reference for all NEXUS agents and ensures consistent, high-quality development practices across all projects.*
