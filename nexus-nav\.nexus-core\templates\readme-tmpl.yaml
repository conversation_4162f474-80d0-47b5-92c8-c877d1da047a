title: "README Documentation Template"
description: "Template for creating comprehensive README files for SaaS projects"
version: "1.0.0"
framework: "NEXUS"

sections:
  - id: header
    title: "Project Header"
    instruction: "Create compelling project header with logo and badges"
    template: |
      # {{project_name}}
      
      {{project_description}}
      
      [![Version](https://img.shields.io/badge/version-{{version}}-blue.svg)]({{version_link}})
      [![License](https://img.shields.io/badge/license-{{license}}-green.svg)]({{license_link}})
      [![Build Status](https://img.shields.io/badge/build-{{build_status}}-{{build_color}}.svg)]({{build_link}})
      
      ## 🚀 Quick Start
      
      {{quick_start_description}}

  - id: features
    title: "Key Features"
    instruction: "Highlight main features and capabilities"
    template: |
      ## ✨ Features
      
      {{feature_list}}
      
      ## 🎯 Use Cases
      
      {{use_cases}}

  - id: tech_stack
    title: "Technology Stack"
    instruction: "Document the complete tech stack"
    template: |
      ## 🛠️ Tech Stack
      
      ### Frontend
      - **Framework**: Next.js 15+ (App Router)
      - **React**: React 19 (Server Components)
      - **TypeScript**: 5.8+
      - **Styling**: Tailwind CSS 4.1.11
      - **UI Components**: {{ui_components}}
      
      ### Backend
      - **Database**: Supabase (PostgreSQL)
      - **Authentication**: Supabase Auth
      - **Storage**: Supabase Storage
      - **Real-time**: Supabase Realtime
      
      ### State Management
      - **Client State**: Zustand 5+
      - **Server State**: TanStack Query v5
      - **Validation**: Valibot v1.1.0
      
      ### Development Tools
      {{dev_tools}}

  - id: installation
    title: "Installation Guide"
    instruction: "Provide step-by-step installation instructions"
    template: |
      ## 📦 Installation
      
      ### Prerequisites
      {{prerequisites}}
      
      ### Quick Setup
      ```bash
      # Clone the repository
      git clone {{repo_url}}
      cd {{project_directory}}
      
      # Install dependencies
      {{package_manager}} install
      
      # Set up environment variables
      cp .env.example .env.local
      # Edit .env.local with your configuration
      
      # Run the development server
      {{package_manager}} run dev
      ```
      
      ### Environment Variables
      ```env
      {{environment_variables}}
      ```
      
      ### Database Setup
      {{database_setup}}

  - id: usage
    title: "Usage Guide"
    instruction: "Provide usage examples and common workflows"
    template: |
      ## 🚀 Usage
      
      ### Basic Usage
      {{basic_usage}}
      
      ### Advanced Features
      {{advanced_features}}
      
      ### API Examples
      ```typescript
      {{api_examples}}
      ```
      
      ### Common Workflows
      {{common_workflows}}

  - id: development
    title: "Development Guide"
    instruction: "Guide for developers contributing to the project"
    template: |
      ## 🔧 Development
      
      ### Development Setup
      ```bash
      # Install dependencies
      {{package_manager}} install
      
      # Start development server
      {{package_manager}} run dev
      
      # Run tests
      {{package_manager}} run test
      
      # Run linting
      {{package_manager}} run lint
      
      # Type checking
      {{package_manager}} run type-check
      ```
      
      ### Project Structure
      ```
      {{project_structure}}
      ```
      
      ### Coding Standards
      {{coding_standards}}
      
      ### Testing Strategy
      {{testing_strategy}}

  - id: deployment
    title: "Deployment Guide"
    instruction: "Deployment instructions for production"
    template: |
      ## 🚀 Deployment
      
      ### Production Build
      ```bash
      {{package_manager}} run build
      {{package_manager}} run start
      ```
      
      ### Deployment Platforms
      {{deployment_platforms}}
      
      ### Environment Configuration
      {{environment_configuration}}
      
      ### Performance Optimization
      {{performance_optimization}}

  - id: api_documentation
    title: "API Documentation"
    instruction: "API reference and examples"
    template: |
      ## 📚 API Documentation
      
      ### Authentication
      {{api_authentication}}
      
      ### Endpoints
      {{api_endpoints}}
      
      ### Error Handling
      {{api_error_handling}}
      
      ### Rate Limiting
      {{api_rate_limiting}}

  - id: contributing
    title: "Contributing Guide"
    instruction: "Guidelines for contributors"
    template: |
      ## 🤝 Contributing
      
      We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.
      
      ### Quick Contribution Steps
      1. Fork the repository
      2. Create a feature branch: `git checkout -b feature/amazing-feature`
      3. Commit your changes: `git commit -m 'Add amazing feature'`
      4. Push to the branch: `git push origin feature/amazing-feature`
      5. Open a Pull Request
      
      ### Development Guidelines
      {{development_guidelines}}
      
      ### Code Review Process
      {{code_review_process}}

  - id: support
    title: "Support and Community"
    instruction: "Support resources and community links"
    template: |
      ## 💬 Support
      
      ### Getting Help
      {{support_channels}}
      
      ### Community
      {{community_links}}
      
      ### Bug Reports
      {{bug_report_process}}
      
      ### Feature Requests
      {{feature_request_process}}

  - id: license
    title: "License and Legal"
    instruction: "License information and legal notices"
    template: |
      ## 📄 License
      
      This project is licensed under the {{license}} License - see the [LICENSE](LICENSE) file for details.
      
      ### Third-Party Licenses
      {{third_party_licenses}}
      
      ### Copyright
      {{copyright_notice}}

placeholders:
  project_name: "Name of the project"
  project_description: "Brief description of what the project does"
  version: "Current version number"
  version_link: "Link to version/releases page"
  license: "License type (e.g., MIT, Apache 2.0)"
  license_link: "Link to license file"
  build_status: "Build status (passing, failing)"
  build_color: "Badge color for build status"
  build_link: "Link to build/CI system"
  quick_start_description: "Brief quick start description"
  feature_list: "List of key features with descriptions"
  use_cases: "Common use cases and scenarios"
  ui_components: "UI component library used"
  dev_tools: "Development tools and utilities"
  prerequisites: "System requirements and prerequisites"
  repo_url: "Repository URL for cloning"
  project_directory: "Project directory name"
  package_manager: "Package manager (npm, pnpm, yarn)"
  environment_variables: "Required environment variables"
  database_setup: "Database setup instructions"
  basic_usage: "Basic usage examples"
  advanced_features: "Advanced feature examples"
  api_examples: "API usage examples in TypeScript"
  common_workflows: "Common user workflows"
  project_structure: "Project directory structure"
  coding_standards: "Coding standards and conventions"
  testing_strategy: "Testing approach and tools"
  deployment_platforms: "Supported deployment platforms"
  environment_configuration: "Production environment setup"
  performance_optimization: "Performance optimization tips"
  api_authentication: "API authentication methods"
  api_endpoints: "API endpoint documentation"
  api_error_handling: "API error handling patterns"
  api_rate_limiting: "API rate limiting information"
  development_guidelines: "Development best practices"
  code_review_process: "Code review workflow"
  support_channels: "Available support channels"
  community_links: "Community and social links"
  bug_report_process: "How to report bugs"
  feature_request_process: "How to request features"
  third_party_licenses: "Third-party license acknowledgments"
  copyright_notice: "Copyright and attribution"

validation:
  required_sections: ["header", "features", "tech_stack", "installation", "usage"]
  tech_stack_compliance: true
  installation_tested: true
  examples_working: true
