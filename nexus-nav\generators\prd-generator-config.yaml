generator:
  name: "PRD Generator Agent"
  version: "1.0.0"
  type: "document-generator"
  framework: "NEXUS"

configuration:
  output_format: "markdown"
  template_structure: "enterprise-prd"
  tech_stack_integration: true
  enterprise_features: true

tech_stack:
  frontend:
    framework: "Next.js 15.4+"
    ui_library: "React 19"
    language: "TypeScript 5.8+"
    routing: "App Router"
    components: "Server Components"
  
  backend:
    platform: "Supabase"
    database: "PostgreSQL"
    auth: "Supabase Auth"
    storage: "Supabase Storage"
    realtime: "Supabase Realtime"
  
  state_management:
    client: "Zustand 5+"
    server: "TanStack Query v5"
    validation: "Valibot v1.1.0"
  
  ui_styling:
    css: "Tailwind CSS 4.0+"
    components: "Shadcn/ui"
    primitives: "Radix UI"
  
  database:
    primary_keys: "XID (pg_idkit)"
    security: "Row Level Security (RLS)"
    search: "pgvector for AI features"

prd_sections:
  executive_summary:
    required: true
    includes:
      - business_objectives
      - target_market
      - success_metrics
      - timeline_overview
  
  product_overview:
    required: true
    includes:
      - value_proposition
      - core_features
      - architecture_overview
      - integration_requirements
  
  user_stories:
    required: true
    format: "Given-When-Then"
    includes:
      - acceptance_criteria
      - user_personas
      - edge_cases
      - accessibility_requirements
  
  technical_requirements:
    required: true
    includes:
      - database_schema
      - api_specifications
      - security_requirements
      - performance_benchmarks
      - scalability_planning
  
  implementation_roadmap:
    required: true
    includes:
      - development_phases
      - feature_prioritization
      - resource_allocation
      - risk_assessment
  
  quality_assurance:
    required: true
    includes:
      - testing_strategy
      - security_validation
      - performance_monitoring
      - deployment_procedures

enterprise_features:
  multi_tenant: true
  rbac_authentication: true
  audit_logging: true
  data_compliance: true
  scalability_planning: true
  monitoring_observability: true

quality_standards:
  documentation:
    clarity: "high"
    completeness: "comprehensive"
    actionability: "specific"
    testability: "measurable"
  
  technical_depth:
    architecture_detail: "detailed"
    implementation_guidance: "specific"
    security_considerations: "comprehensive"
    performance_requirements: "quantified"

output_requirements:
  file_format: "markdown"
  naming_convention: "[project-name]-prd.md"
  structure: "hierarchical"
  cross_references: true
  implementation_ready: true

validation_criteria:
  technical_feasibility: true
  business_alignment: true
  implementation_clarity: true
  testing_coverage: true
  security_compliance: true
