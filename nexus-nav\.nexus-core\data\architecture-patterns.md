# NEXUS Architecture Patterns

## 🏗️ **Proven Architecture Patterns for Scalable SaaS**

### Next.js App Router Patterns

#### Server Component First Architecture
```yaml
pattern_name: "Server Component First"
use_case: "Default approach for all components"
benefits:
  - Better performance with server-side rendering
  - Reduced client-side JavaScript bundle
  - Direct database access without API layer
  - Improved SEO and initial page load

implementation:
  default_choice: "Server Components for all new components"
  client_components: "Only when interactivity is required"
  data_fetching: "At component level using async/await"
  caching: "Built-in Next.js caching with revalidation"

example: |
  // Server Component (default)
  export default async function UserList() {
    const users = await getUsers(); // Direct database call
    return <div>{users.map(user => <UserCard key={user.id} user={user} />)}</div>;
  }
  
  // Client Component (when needed)
  'use client';
  export function InteractiveButton() {
    const [count, setCount] = useState(0);
    return <button onClick={() => setCount(c => c + 1)}>{count}</button>;
  }
```

#### Progressive Enhancement Pattern
```yaml
pattern_name: "Progressive Enhancement"
use_case: "Building resilient user experiences"
benefits:
  - Works without JavaScript
  - Enhanced with JavaScript when available
  - Better accessibility and performance
  - Graceful degradation

implementation:
  base_functionality: "HTML forms and server actions"
  enhancement_layer: "Client-side interactivity"
  fallback_strategy: "Server-side form handling"
  progressive_loading: "Streaming and Suspense"

example: |
  // Form that works without JavaScript
  export default function ContactForm() {
    async function submitForm(formData: FormData) {
      'use server';
      // Server action handles form submission
      await saveContact(formData);
    }
    
    return (
      <form action={submitForm}>
        <input name="email" type="email" required />
        <button type="submit">Submit</button>
      </form>
    );
  }
```

### Data Architecture Patterns

#### Repository Pattern with Supabase
```yaml
pattern_name: "Repository Pattern"
use_case: "Centralized data access layer"
benefits:
  - Consistent data access patterns
  - Easy testing with mock repositories
  - Type-safe database operations
  - Centralized error handling

implementation:
  structure: "lib/repositories/"
  naming: "EntityRepository class"
  methods: "CRUD operations with TypeScript"
  error_handling: "Centralized error management"

example: |
  // lib/repositories/UserRepository.ts
  export class UserRepository {
    constructor(private supabase: SupabaseClient) {}
    
    async findById(id: string): Promise<User | null> {
      const { data, error } = await this.supabase
        .from('users')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) throw new DatabaseError(error.message);
      return data;
    }
    
    async create(user: CreateUserInput): Promise<User> {
      const { data, error } = await this.supabase
        .from('users')
        .insert(user)
        .select()
        .single();
      
      if (error) throw new DatabaseError(error.message);
      return data;
    }
  }
```

#### Query Object Pattern
```yaml
pattern_name: "Query Object Pattern"
use_case: "Complex database queries with type safety"
benefits:
  - Reusable query logic
  - Type-safe query building
  - Easy testing and mocking
  - Consistent query patterns

implementation:
  structure: "lib/queries/"
  naming: "EntityQueries class"
  methods: "Chainable query builders"
  types: "TypeScript query interfaces"

example: |
  // lib/queries/UserQueries.ts
  export class UserQueries {
    constructor(private supabase: SupabaseClient) {}
    
    active() {
      return this.supabase
        .from('users')
        .select('*')
        .eq('status', 'active');
    }
    
    withProfile() {
      return this.supabase
        .from('users')
        .select(`
          *,
          profile:profiles(*)
        `);
    }
    
    byRole(role: UserRole) {
      return this.supabase
        .from('users')
        .select('*')
        .eq('role', role);
    }
  }
```

### State Management Patterns

#### Zustand Store Pattern
```yaml
pattern_name: "Zustand Store Pattern"
use_case: "Client-side state management"
benefits:
  - Minimal boilerplate
  - TypeScript support
  - Devtools integration
  - Flexible store composition

implementation:
  structure: "stores/"
  naming: "useEntityStore"
  slicing: "Feature-based store slices"
  persistence: "Optional localStorage persistence"

example: |
  // stores/useUserStore.ts
  interface UserState {
    user: User | null;
    isLoading: boolean;
    setUser: (user: User | null) => void;
    updateUser: (updates: Partial<User>) => void;
    clearUser: () => void;
  }
  
  export const useUserStore = create<UserState>((set, get) => ({
    user: null,
    isLoading: false,
    
    setUser: (user) => set({ user }),
    
    updateUser: (updates) => set((state) => ({
      user: state.user ? { ...state.user, ...updates } : null
    })),
    
    clearUser: () => set({ user: null })
  }));
```

#### TanStack Query Pattern
```yaml
pattern_name: "TanStack Query Pattern"
use_case: "Server state management with caching"
benefits:
  - Automatic caching and revalidation
  - Background updates
  - Optimistic updates
  - Error handling and retries

implementation:
  structure: "hooks/"
  naming: "useEntityQuery, useEntityMutation"
  keys: "Hierarchical query keys"
  invalidation: "Smart cache invalidation"

example: |
  // hooks/useUsers.ts
  export function useUsers(filters?: UserFilters) {
    return useQuery({
      queryKey: ['users', filters],
      queryFn: () => userRepository.findMany(filters),
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  }
  
  export function useCreateUser() {
    const queryClient = useQueryClient();
    
    return useMutation({
      mutationFn: (user: CreateUserInput) => userRepository.create(user),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['users'] });
      },
    });
  }
```

### Security Patterns

#### Row Level Security (RLS) Pattern
```yaml
pattern_name: "Row Level Security Pattern"
use_case: "Multi-tenant data isolation"
benefits:
  - Database-level security
  - Automatic data filtering
  - Prevents data leaks
  - Scalable multi-tenancy

implementation:
  strategy: "User-based and organization-based RLS"
  policies: "Comprehensive policy coverage"
  testing: "Policy testing procedures"
  performance: "Optimized policy queries"

example: |
  -- Enable RLS on users table
  ALTER TABLE users ENABLE ROW LEVEL SECURITY;
  
  -- Policy: Users can only see their own data
  CREATE POLICY "Users can view own data" ON users
    FOR SELECT USING (auth.uid() = id);
  
  -- Policy: Users can update their own data
  CREATE POLICY "Users can update own data" ON users
    FOR UPDATE USING (auth.uid() = id);
  
  -- Policy: Organization members can see each other
  CREATE POLICY "Organization members can view" ON users
    FOR SELECT USING (
      organization_id IN (
        SELECT organization_id FROM user_organizations 
        WHERE user_id = auth.uid()
      )
    );
```

#### Input Validation Pattern
```yaml
pattern_name: "Input Validation Pattern"
use_case: "Comprehensive input validation with Valibot"
benefits:
  - Type-safe validation
  - Consistent error messages
  - Client and server validation
  - Tree-shaking friendly

implementation:
  library: "Valibot v1.1.0"
  structure: "schemas/"
  naming: "EntitySchema"
  usage: "Client and server validation"

example: |
  // schemas/UserSchema.ts
  import { object, string, email, minLength } from 'valibot';
  
  export const CreateUserSchema = object({
    email: string([email('Invalid email format')]),
    password: string([minLength(8, 'Password must be at least 8 characters')]),
    name: string([minLength(2, 'Name must be at least 2 characters')]),
  });
  
  export type CreateUserInput = Input<typeof CreateUserSchema>;
  
  // Usage in API route
  export async function POST(request: Request) {
    const body = await request.json();
    const result = safeParse(CreateUserSchema, body);
    
    if (!result.success) {
      return NextResponse.json({ error: result.issues }, { status: 400 });
    }
    
    // Process validated data
    const user = await userRepository.create(result.output);
    return NextResponse.json(user);
  }
```

### Performance Patterns

#### Caching Strategy Pattern
```yaml
pattern_name: "Multi-Level Caching"
use_case: "Optimizing performance for millions of users"
benefits:
  - Reduced database load
  - Faster response times
  - Better user experience
  - Cost optimization

implementation:
  levels: "Browser, CDN, Application, Database"
  strategies: "Cache-first, Network-first, Stale-while-revalidate"
  invalidation: "Smart cache invalidation"
  monitoring: "Cache hit rate monitoring"

example: |
  // Next.js caching with revalidation
  export async function getUsers() {
    const response = await fetch('/api/users', {
      next: { 
        revalidate: 300, // 5 minutes
        tags: ['users'] 
      }
    });
    return response.json();
  }
  
  // Manual cache invalidation
  import { revalidateTag } from 'next/cache';
  
  export async function createUser(user: CreateUserInput) {
    const newUser = await userRepository.create(user);
    revalidateTag('users'); // Invalidate users cache
    return newUser;
  }
```

#### Database Optimization Pattern
```yaml
pattern_name: "Database Optimization"
use_case: "High-performance database operations"
benefits:
  - Faster query execution
  - Reduced resource usage
  - Better scalability
  - Lower costs

implementation:
  indexing: "Strategic index creation"
  queries: "Optimized query patterns"
  connections: "Connection pooling"
  monitoring: "Query performance monitoring"

example: |
  -- Strategic indexing for common queries
  CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
  CREATE INDEX CONCURRENTLY idx_users_organization_status 
    ON users(organization_id, status) WHERE status = 'active';
  
  -- Optimized query with proper joins
  SELECT u.*, p.avatar_url, o.name as organization_name
  FROM users u
  LEFT JOIN profiles p ON u.id = p.user_id
  LEFT JOIN organizations o ON u.organization_id = o.id
  WHERE u.status = 'active'
    AND u.organization_id = $1
  ORDER BY u.created_at DESC
  LIMIT 20;
```

### Error Handling Patterns

#### Centralized Error Handling Pattern
```yaml
pattern_name: "Centralized Error Handling"
use_case: "Consistent error management across the application"
benefits:
  - Consistent error responses
  - Centralized logging
  - Better debugging
  - User-friendly error messages

implementation:
  structure: "lib/errors/"
  types: "Custom error classes"
  handling: "Global error handlers"
  logging: "Structured error logging"

example: |
  // lib/errors/AppError.ts
  export class AppError extends Error {
    constructor(
      message: string,
      public statusCode: number = 500,
      public code: string = 'INTERNAL_ERROR'
    ) {
      super(message);
      this.name = 'AppError';
    }
  }
  
  export class ValidationError extends AppError {
    constructor(message: string, public field?: string) {
      super(message, 400, 'VALIDATION_ERROR');
      this.name = 'ValidationError';
    }
  }
  
  // Global error handler
  export function handleApiError(error: unknown): NextResponse {
    if (error instanceof AppError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.statusCode }
      );
    }
    
    // Log unexpected errors
    console.error('Unexpected error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error', code: 'INTERNAL_ERROR' },
      { status: 500 }
    );
  }
```

## 🎯 **Pattern Selection Guidelines**

### When to Use Each Pattern

1. **Server Component First**: Default for all new components
2. **Repository Pattern**: For complex data access logic
3. **Query Object Pattern**: For reusable, complex queries
4. **Zustand Store**: For client-side application state
5. **TanStack Query**: For server state and caching
6. **RLS Pattern**: For multi-tenant applications
7. **Input Validation**: For all user inputs
8. **Multi-Level Caching**: For high-traffic applications
9. **Database Optimization**: For performance-critical queries
10. **Centralized Error Handling**: For all applications

### Pattern Combinations

- **Server Component + Repository + RLS**: Secure data access
- **Client Component + Zustand + TanStack Query**: Interactive features
- **Input Validation + Error Handling**: Robust API endpoints
- **Caching + Database Optimization**: High-performance applications

These patterns are battle-tested for scalable SaaS applications serving millions of users with Next.js 15+, React 19, and Supabase.
